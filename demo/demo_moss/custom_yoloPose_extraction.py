# -*-coding:utf-8-*-
"""
created by Moss 2025/03/06 17:01

"""
import pickle
from pathlib import Path
from tqdm import tqdm
import argparse

import numpy as np


def mmengine_dump(obj, file_path, **kwargs):
    """
    参考mmengine.dump(anno, out) 将列表信息保存成pkl
    obj： 要序列化的对象
    file_path: 输出文件夹路径
    **kwargs: 传递给pickle.dump的额外参数
    """
    kwargs.setdefault('protocol', 2)        # 设置兼容老版本python
    with open(file_path, 'wb') as file:
        pickle.dump(obj, file, **kwargs)

    return


def mrlines(fname, sp='\n') -> list:
    """获取数据集的正确路径和标签"""
    f = open(fname).read().split(sp)
    while f != [] and f[-1] == '':
        f = f[:-1]

    lines = [x.split() for x in f]  # 将标签独立出来
    return lines


def read_file_pkl(pkl_name:str) -> dict:
    """读取pkl信息"""
    if not Path(pkl_name).exists():
        return {}
    with open(pkl_name, 'rb') as fi:
        data = pickle.load(fi)
    return data


def pose_getResult(anno_in, base_info):
    """
    将读取到的样本 -> 需要的数据字典
        num_person = 1      # 默认单人
    """
    if not len(anno_in):
        return {}
    anno = dict()

    keypoint_lst = anno_in.get('pred_skpts')
    skpts = [skpt_ts[1][..., :2] for skpt_ts in keypoint_lst if skpt_ts is not None]
    scores = [skpt_ts[1][..., 2] for skpt_ts in keypoint_lst if skpt_ts is not None]

    anno['keypoint'] =np.stack(skpts, axis=1)           # (N, T, 17, 2)
    anno['keypoint_score'] =np.stack(scores, axis=1)           # (N, T, 17)

    # 其他信息汇总
    anno['img_shape'] = anno_in.get('img_shape')
    anno['original_shape'] = anno_in.get('oriVid_shape')
    anno['total_frames'] = len(skpts)
    anno['vid_frames'] = anno_in.get('total_frames')        # 这里补充1个视频部分的原始帧数(包含跟踪丢失和出画面的帧)
    anno.update(base_info)

    del anno_in
    return anno


def dump_all_label_ann(args) ->list:
    lines = mrlines(args.video_list)


    # * We set 'frame_dir' as the base name (w/o. suffix) of each video
    if len(lines[0]) == 2:
        annos = [dict(frame_dir=x[0].split('.')[0], filename=x[0], label=int(x[1].split('_')[0])) for x in lines]
    else:
        # 没有标签/标签错误，仅有pkl的情况，可能用于样本推理
        annos = [dict(frame_dir=x[0].split('.')[0], filename=x[0]) for x in lines]

    results = []
    for anno_info in tqdm(annos):
        pkl_name = anno_info.get('filename', 'None')
        anno_dict = read_file_pkl(pkl_name)
        anno = pose_getResult(anno_dict, anno_info)

        results.append(anno)


    mmengine_dump(results, args.output)



    return results


def dump_trainVal_ann(args):
    train_lines = mrlines(args.tain_txt_path)
    val_lines = mrlines(args.val_txt_path)

    split = dict()
    split['train'] = [x[0].split('.')[0] for x in train_lines]          # file_name_跑道_frame.pkl
    split['val'] = [x[0].split('.')[0] for x in val_lines]

    mmengine_dump(dict(split=split, annotations=annotations), args.out_pose_pkl)

    return


def parse_args():
    parser = argparse.ArgumentParser(
        description='Generate 2D pose annotations for a custom video dataset')

    parser.add_argument('--video-list', type=str, help='the list of source videos',
                        default="/dockerSpace/train/datasets/all_label.txt")
    parser.add_argument('--tain_txt', type=str, help='默认在video_list同级', default='train_label.txt')
    parser.add_argument('--val_txt', type=str, help='默认在video_list同级', default='val_label.txt')

    parser.add_argument('--output', type=str, help='output pickle name, 默认存储在video_list同级', default='None')
    parser.add_argument('--out_pose_pkl', type=str, help='output pickle name, 默认存储在video_list同级', default='None')

    args = parser.parse_args()

    if not Path(args.output).is_file():
        args.output = Path(args.video_list).parent / 'all_label_ann.pkl'
    if not Path(args.out_pose_pkl).is_file():
        args.out_pose_pkl = Path(args.video_list).parent / 'trainval_yolopose.pkl'

    args.tain_txt_path = Path(args.video_list).parent / args.tain_txt
    args.val_txt_path = Path(args.video_list).parent / args.val_txt

    return args


if __name__ == '__main__':
    args = parse_args()

    # Step：生成大Pkl
    annotations = dump_all_label_ann(args)
    print('Generate all_label_ann.pkl', end=';')

    # Step: 生成训练、验证的pkl
    dump_trainVal_ann(args)
    print('Generate trainval_yolopose.pkl success!')
