#%%
from operator import itemgetter
from mmaction.apis import init_recognizer, inference_recognizer
#%%
config_file = '../demo/demo_configs/tsn_r50_1x1x8_video_infer.py'
# download the checkpoint from model zoo and put it in `checkpoints/`
checkpoint_file = '../checkpoints/tsn_r50_8xb32-1x1x8-100e_kinetics400-rgb_20220818-2692d16c.pth'
#%%
# build the model from a config file and a checkpoint file
model = init_recognizer(config_file, checkpoint_file, device='cpu')
#%%
# test a single video and show the result:
video = 'demo.mp4'
label = '../tools/data/kinetics/label_map_k400.txt'
results = inference_recognizer(model, video)

pred_scores = results.pred_score.tolist()
score_tuples = tuple(zip(range(len(pred_scores)), pred_scores))
score_sorted = sorted(score_tuples, key=itemgetter(1), reverse=True)
top5_label = score_sorted[:5]

labels = open(label).readlines()
labels = [x.strip() for x in labels]
results = [(labels[k[0]], k[1]) for k in top5_label]
#%%
# show the results
for result in results:
    print(f'{result[0]}: ', result[1])