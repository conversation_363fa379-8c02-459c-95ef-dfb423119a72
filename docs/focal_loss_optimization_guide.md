# Focal Loss 优化指南

## 概述

本指南介绍了为仰卧起坐作弊检测项目实施的Focal Loss优化方案，旨在提高异常检出率并降低正常误判率。

## 主要改进

### 1. 损失函数优化
- **Focal Loss**: 替代标准交叉熵，自动关注困难样本
- **Label Smoothing**: 防止模型过度自信，提高泛化能力
- **动态权重**: 基于类别分布自动计算alpha权重

### 2. 训练策略优化
- **动态类别权重调整**: 每10个epoch根据验证集表现调整权重
- **困难样本挖掘**: 识别并重点训练困难样本
- **正则化增强**: 增加dropout率，减少过拟合
- **优化器策略**: 使用SGD替代AdamW，更适合Focal Loss的动态权重特性
- **分层学习率**: 针对不同模块采用差异化学习率策略

## 文件结构

```
mmaction2-main0807/
├── mmaction/
│   ├── models/losses/
│   │   └── focal_loss.py                    # Focal Loss实现
│   ├── utils/
│   │   └── class_weight_utils.py           # 类别权重计算工具
│   ├── engine/hooks/
│   │   ├── class_weight_hook.py            # 动态权重调整Hook
│   │   └── hard_sample_hook.py             # 困难样本挖掘Hook
│   └── datasets/
│       └── multimodal_dataset.py           # 数据集类别统计功能
├── configs/recognition/Multimodal/
│   └── multimodal_poseGCN-rgbR50_fusion_focal.py  # Focal Loss配置文件
├── tools/
│   ├── train_m2.py                         # 原始训练脚本（保持不变）
│   ├── train_focal.py                      # Focal Loss优化训练脚本
│   └── test_focal_loss_components.py       # 组件测试脚本
└── docs/
    └── focal_loss_optimization_guide.md    # 本指南
```

## 使用方法

### 1. 基本训练命令

```bash
# 激活conda环境
source /home/<USER>/anaconda3/bin/activate moss_10

# 使用Focal Loss优化训练脚本
cd /media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2-main0807
python tools/train_focal.py

# 或者指定配置文件
python tools/train_focal.py --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_focal.py
```

### 2. 高级训练选项

```bash
# 指定工作目录
python tools/train_focal.py --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_focal.py --work-dir ./work_dirs/focal_experiment_1

# 启用自动混合精度训练
python tools/train_focal.py --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_focal.py --amp

# 从检查点恢复训练
python tools/train_focal.py --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_focal.py --resume auto

# 覆盖配置参数
python tools/train_focal.py --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_focal.py --cfg-options model.cls_head.loss_cls.gamma=3.0

# 使用原始配置但启用Focal Loss（通过cfg-options）
python tools/train_focal.py --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py --cfg-options model.cls_head.loss_cls.type=FocalLossWithSmoothing model.cls_head.loss_cls.alpha=auto model.cls_head.loss_cls.gamma=2.0
```

### 3. 测试组件

```bash
# 测试所有Focal Loss组件
python tools/test_focal_loss_components.py
```

## 配置参数说明

### Focal Loss 参数

```python
loss_cls = dict(
    type='FocalLossWithSmoothing',
    alpha='auto',              # 自动计算类别权重
    gamma=2.0,                 # 聚焦参数，控制难易样本权重差异
    label_smoothing=0.1,       # 标签平滑参数
    class_counts=[6258, 5494, 4751],  # 类别分布（会动态更新）
    reduction='mean'
)
```

### 动态权重调整Hook

```python
dict(
    type='ClassWeightHook',
    update_interval=10,        # 每10个epoch更新权重
    target_ratios=[1.0, 1.2, 1.1],  # 目标权重比例
    min_weight=0.5,           # 最小权重限制
    max_weight=3.0,           # 最大权重限制
    target_metrics=dict(
        abnormal_detection_rate=0.97,    # 异常检出率目标
        normal_false_positive_rate=0.015  # 正常误判率目标
    )
)
```

### 困难样本挖掘Hook

```python
dict(
    type='HardSampleHook',
    update_interval=5,         # 每5个epoch更新策略
    difficulty_threshold=0.7,  # 困难样本阈值
    hard_sample_weight=2.0,    # 困难样本权重倍数
    queue_size=1000           # 困难样本队列大小
)
```

## 优化器和学习率配置详解

### 优化器选择：SGD vs AdamW

#### 为什么选择SGD？

1. **理论依据**：
   - Focal Loss通过动态调整损失权重来处理类别不平衡和困难样本
   - SGD的直接梯度更新机制更适合处理动态权重的损失函数
   - AdamW的自适应学习率可能会"平滑"掉Focal Loss想要强调的困难样本信号

2. **文献支持**：
   - 原始Focal Loss论文（Lin et al., 2017）使用SGD优化器
   - 大多数Focal Loss实现和实验都采用SGD

3. **实践依据**：
   - 在类别不平衡任务中，SGD通常比自适应优化器表现更稳定
   - SGD在多模态融合任务中的收敛更可预测

#### SGD配置参数

```python
optimizer=dict(
    type='SGD',
    lr=0.01,        # 基础学习率（比AdamW的0.001更大）
    momentum=0.9,   # 标准动量设置
    weight_decay=0.0001
)
```

### 分层学习率策略

#### 设计原理

不同模块具有不同的初始化状态和学习需求：

1. **预训练模块**：已有良好特征表示，需要较小学习率微调
2. **随机初始化模块**：需要从零学习，但过大学习率可能导致不稳定
3. **任务特定模块**：需要快速适应目标任务

#### 具体配置

```python
paramwise_cfg=dict(
    custom_keys={
        'image_backbone': dict(lr_mult=0.1),    # 预训练MobileNetV2TSM
        'pose_backbone': dict(lr_mult=1.0),     # 预训练STGCN
        'fusion_neck': dict(lr_mult=0.5),       # 随机初始化融合模块
        'cls_head': dict(lr_mult=2.0)           # 随机初始化分类头
    }
)
```

#### 实际学习率分析

| 模块 | lr_mult | 实际学习率 | 模块特性 | 设计依据 |
|------|---------|------------|----------|----------|
| `image_backbone` | 0.1 | 0.001 | 预训练MobileNetV2TSM | 微调预训练特征，保护已学到的表示 |
| `pose_backbone` | 1.0 | 0.01 | 预训练STGCN | 预训练模块，使用标准学习率 |
| `fusion_neck` | 0.5 | 0.005 | 随机初始化融合模块 | 中等学习率平衡学习速度和稳定性 |
| `cls_head` | 2.0 | 0.02 | 随机初始化分类头 | 较大学习率快速适应分类任务 |

#### 学习率调度器调整

```python
param_scheduler = [
    dict(
        type='LinearLR',
        start_factor=0.1,    # 从0.01调整为0.1，适应SGD特性
        by_epoch=True,
        begin=0,
        end=5
    ),
    dict(
        type='CosineAnnealingLR',
        T_max=95,
        by_epoch=True,
        begin=5,
        end=100
        # 移除eta_min，让学习率自然衰减到0
    )
]
```

### 配置演进历史

#### v1.0 基础配置（存在问题）
- 优化器：AdamW，lr=0.001
- 学习率：所有模块统一使用相同的lr_mult

#### v2.0 优化器调整（部分问题）
- 优化器：SGD，lr=0.001（学习率过小）
- 学习率：添加了decay_mult=1.0（冗余配置）

#### v3.0 当前基准配置（推荐）
- 优化器：SGD，lr=0.01（适合SGD的学习率）
- 学习率：分层策略，针对不同模块特性优化
- 移除冗余配置，保持配置清晰

### 基于训练日志的调优指南

#### 监控指标

训练过程中重点关注以下学习率相关指标：

1. **收敛速度**：
   - 各模块的梯度范数变化
   - 损失函数下降速度
   - 验证集指标提升速度

2. **训练稳定性**：
   - 损失函数是否出现震荡
   - 梯度是否出现爆炸或消失
   - 不同模块的参数更新幅度

3. **模块协调性**：
   - 不同模态特征的融合效果
   - 各模块的学习进度是否协调

#### 调优策略

根据训练日志进行学习率调整：

1. **如果fusion_neck收敛过慢**：
   ```python
   'fusion_neck': dict(lr_mult=0.8)  # 从0.5提升到0.8
   ```

2. **如果cls_head训练不稳定**：
   ```python
   'cls_head': dict(lr_mult=1.5)     # 从2.0降低到1.5
   ```

3. **如果整体收敛过慢**：
   ```python
   lr=0.02  # 将基础学习率从0.01提升到0.02
   ```

4. **如果出现过拟合**：
   ```python
   # 降低非预训练模块的学习率
   'fusion_neck': dict(lr_mult=0.3),
   'cls_head': dict(lr_mult=1.2)
   ```

## 预期效果

基于实验分析，Focal Loss优化预期能够：

1. **减少过拟合**: 通过Label Smoothing和动态权重调整
2. **提高异常检出率**: 从96.53%提升至97%+
3. **降低正常误判率**: 从1.65%降低至1.5%以下
4. **增强模型稳定性**: 通过困难样本挖掘和正则化

## 监控指标

训练过程中重点关注以下指标：

### 性能指标
- `abnormal_detection_rate`: 异常检出率
- `normal_false_positive_rate`: 正常误判率
- `class_weight_*`: 各类别权重变化
- `hard_sample_ratio`: 困难样本比例
- `difficulty_threshold`: 困难样本阈值

### 学习率相关指标
- `lr/image_backbone`: image_backbone实际学习率
- `lr/pose_backbone`: pose_backbone实际学习率
- `lr/fusion_neck`: fusion_neck实际学习率
- `lr/cls_head`: cls_head实际学习率
- `grad_norm`: 梯度范数，监控训练稳定性
- `loss/train`: 训练损失变化趋势
- `loss/val`: 验证损失，监控过拟合

## 故障排除

### 常见问题

1. **模块导入错误**: 确保激活正确的conda环境
2. **配置文件错误**: 检查配置文件语法和参数
3. **内存不足**: 减小batch_size或使用梯度累积
4. **收敛缓慢**: 调整学习率或Focal Loss参数
5. **训练不稳定**: 检查学习率设置，特别是随机初始化模块的学习率
6. **模态融合效果差**: 调整fusion_neck的学习率，确保不同模态协调学习
7. **分类头收敛慢**: 适当提高cls_head的学习率倍数

### 调试命令

```bash
# 检查配置文件
python -c "from mmengine.config import Config; cfg = Config.fromfile('configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_focal.py'); print('配置正确')"

# 测试单个组件
python -c "from mmaction.models.losses.focal_loss import FocalLoss; print('Focal Loss导入成功')"

# 检查学习率配置
python -c "
from mmengine.config import Config
cfg = Config.fromfile('configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_focal.py')
print('基础学习率:', cfg.optim_wrapper.optimizer.lr)
for key, value in cfg.optim_wrapper.paramwise_cfg.custom_keys.items():
    actual_lr = cfg.optim_wrapper.optimizer.lr * value['lr_mult']
    print(f'{key}: lr_mult={value[\"lr_mult\"]}, 实际学习率={actual_lr}')
"

# 验证预训练模型路径
python -c "
import os
models = ['./stgcn_8xb16-bone-u100-80e_ntu120-xsub-keypoint-2d_20221129-131e63c3.pth',
          './tsm_imagenet-pretrained-mobilenetv2_8xb16-1x1x8-100e_kinetics400-rgb_20230414-401127fd.pth']
for model in models:
    if os.path.exists(model):
        print(f'✅ {model} 存在')
    else:
        print(f'❌ {model} 不存在')
"
```

## 版本信息

- **创建日期**: 2025-08-07
- **最后更新**: 2025-08-08
- **作者**: @Moss
- **版本**: v3.0 (分层学习率基准版本)
- **基于**: MMAction2 + 仰卧起坐作弊检测项目

### 版本历史

- **v1.0** (2025-08-07): 初始版本，基础Focal Loss实现
- **v2.0** (2025-08-08): 优化器调整，SGD替代AdamW
- **v3.0** (2025-08-08): 分层学习率策略，当前基准配置

### 当前基准配置摘要

```python
# 优化器配置
optimizer=dict(type='SGD', lr=0.01, momentum=0.9, weight_decay=0.0001)

# 分层学习率配置
paramwise_cfg=dict(
    custom_keys={
        'image_backbone': dict(lr_mult=0.1),    # 实际lr=0.001
        'pose_backbone': dict(lr_mult=1.0),     # 实际lr=0.01
        'fusion_neck': dict(lr_mult=0.5),       # 实际lr=0.005
        'cls_head': dict(lr_mult=2.0)           # 实际lr=0.02
    }
)

# Focal Loss配置
loss_cls=dict(
    type='FocalLossWithSmoothing',
    alpha='auto',
    gamma=2.0,
    label_smoothing=0.1,
    class_counts=[6258, 5494, 4751]
)
```

## 联系方式

如有问题或建议，请联系项目维护者。
