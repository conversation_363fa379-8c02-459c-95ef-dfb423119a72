# ExpI：注意力引导的模态选择实验详细设计

## 实验背景

基于ReadMoss.md中的多模态特征互补性分析：

- **未抱头行为检测**：需要密集的17个关节点×35帧的骨骼序列精确捕捉手部位置变化
- **借助外力作弊检测**：需要3帧关键时刻的RGB图像识别工具、绳子、他人协助等视觉线索  
- **正常动作识别**：需要骨骼+图像特征联合判断确保"全程双手抱头且无外力"

ExpI的核心思路是让模型**自动学习**对不同类型样本使用不同模态的权重，而不是人为设定固定的融合策略。

## 核心创新点

### 1. 样本级模态重要性预测

```python
class ModalityImportancePredictor(nn.Module):
    """样本级模态重要性预测器"""
    def __init__(self, pose_dim=256, img_dim=320, hidden_dim=128):
        super().__init__()
        # 独立预测分支
        self.pose_importance = nn.Sequential(
            nn.Linear(pose_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )
        self.img_importance = nn.Sequential(
            nn.Linear(img_dim, hidden_dim), 
            nn.ReLU(),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )
        
        # 交互预测分支（考虑跨模态信息）
        self.cross_modal_predictor = nn.Sequential(
            nn.Linear(pose_dim + img_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 2),
            nn.Softmax(dim=1)
        )
        
    def forward(self, pose_feat, img_feat):
        # 独立预测
        pose_weight = self.pose_importance(pose_feat.mean(dim=1))  # [B, 1]
        img_weight = self.img_importance(img_feat)  # [B, 1]
        
        # 交互预测
        concat_feat = torch.cat([pose_feat.mean(dim=1), img_feat], dim=1)
        cross_weights = self.cross_modal_predictor(concat_feat)  # [B, 2]
        
        # 融合两种预测结果
        final_pose_weight = 0.3 * pose_weight + 0.7 * cross_weights[:, 0:1]
        final_img_weight = 0.3 * img_weight + 0.7 * cross_weights[:, 1:2]
        
        return final_pose_weight, final_img_weight
```

### 2. 自适应多模态融合模块

```python
class AdaptiveMultiModalFusionNeck(nn.Module):
    """自适应多模态融合颈部网络"""
    def __init__(self, pose_feat_dim=256, img_feat_dim=320, fusion_dim=512, 
                 dropout=0.5, modality_predictor_config=None):
        super().__init__()
        
        # 原有的注意力融合模块
        self.attention_fusion = MultiModalFusionNeck(
            pose_feat_dim, img_feat_dim, fusion_dim, 
            fusion_type='attention', dropout=dropout
        )
        
        # 新增：模态重要性预测器
        self.modality_predictor = ModalityImportancePredictor(
            pose_feat_dim, img_feat_dim, 
            modality_predictor_config.get('hidden_dim', 128)
        )
        
        # 权重感知的特征重标定
        self.weight_calibrator = nn.Sequential(
            nn.Linear(fusion_dim, fusion_dim // 4),
            nn.ReLU(),
            nn.Linear(fusion_dim // 4, fusion_dim),
            nn.Sigmoid()
        )
        
    def forward(self, pose_feat, img_feat):
        # 1. 预测模态重要性权重
        pose_weight, img_weight = self.modality_predictor(pose_feat, img_feat)
        
        # 2. 基于权重重新标定特征
        weighted_pose_feat = pose_feat * pose_weight.unsqueeze(-1)
        weighted_img_feat = img_feat * img_weight.unsqueeze(-1)
        
        # 3. 标准注意力融合
        fusion_feat = self.attention_fusion(weighted_pose_feat, weighted_img_feat)
        
        # 4. 权重感知的后处理
        calibrated_feat = fusion_feat * self.weight_calibrator(fusion_feat)
        
        # 返回融合特征和权重（用于可视化和损失计算）
        return calibrated_feat, {
            'pose_weight': pose_weight,
            'img_weight': img_weight
        }
```

### 3. 辅助损失设计

```python
class AdaptiveLoss(nn.Module):
    """自适应损失函数"""
    def __init__(self, aux_loss_weight=0.1):
        super().__init__()
        self.aux_loss_weight = aux_loss_weight
        self.main_loss = nn.CrossEntropyLoss()
        
    def forward(self, logits, labels, modality_weights, epoch=0):
        # 主分类损失
        main_loss = self.main_loss(logits, labels)
        
        # 辅助损失：模态权重的合理性约束
        pose_weights, img_weights = modality_weights['pose_weight'], modality_weights['img_weight']
        
        # 约束1：权重和应该接近1（平衡约束）
        weight_sum = pose_weights + img_weights
        balance_loss = F.mse_loss(weight_sum, torch.ones_like(weight_sum))
        
        # 约束2：基于标签的期望权重分布
        expected_pose_weights = self._get_expected_weights(labels, modality='pose')
        expected_img_weights = self._get_expected_weights(labels, modality='image')
        
        expectation_loss = (
            F.mse_loss(pose_weights, expected_pose_weights) + 
            F.mse_loss(img_weights, expected_img_weights)
        )
        
        # 动态调整辅助损失权重（训练后期降低）
        dynamic_aux_weight = self.aux_loss_weight * (0.9 ** (epoch // 10))
        
        total_loss = main_loss + dynamic_aux_weight * (balance_loss + expectation_loss)
        
        return total_loss, {
            'main_loss': main_loss,
            'balance_loss': balance_loss,
            'expectation_loss': expectation_loss
        }
    
    def _get_expected_weights(self, labels, modality):
        """基于类别标签生成期望的模态权重"""
        batch_size = labels.size(0)
        expected_weights = torch.zeros(batch_size, 1, device=labels.device)
        
        for i, label in enumerate(labels):
            if modality == 'pose':
                # 未抱头（label=2）更依赖骨骼特征
                if label == 2:  # no_hug
                    expected_weights[i] = 0.8
                elif label == 1:  # cheat  
                    expected_weights[i] = 0.3
                else:  # normal
                    expected_weights[i] = 0.5
            else:  # image
                # 作弊（label=1）更依赖图像特征
                if label == 1:  # cheat
                    expected_weights[i] = 0.8  
                elif label == 2:  # no_hug
                    expected_weights[i] = 0.3
                else:  # normal
                    expected_weights[i] = 0.5
                    
        return expected_weights
```

## 实现文件清单

### 1. 新增模型组件

需要在以下位置添加新的Python文件：

```
mmaction/models/necks/adaptive_fusion_neck.py     # 自适应融合模块
mmaction/models/heads/adaptive_fusion_head.py     # 自适应分类头  
mmaction/models/losses/adaptive_loss.py           # 自适应损失函数
mmaction/engine/hooks/modality_hooks.py           # 模态权重记录Hook
```

### 2. 配置文件

```
configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_expI_adaptive_modality.py
```

### 3. 训练脚本修改

需要在现有训练脚本中添加：

- 模态权重可视化
- 辅助损失监控
- 权重分布统计

## 预期效果与验证指标

### 1. 性能指标

- **主要目标**：正常误判率 < 1.0%，作弊检出率 > 0.98
- **稳定性**：训练曲线更平滑，前40个epoch内收敛
- **可解释性**：模态权重分布符合预期（未抱头样本骨骼权重高，作弊样本图像权重高）

### 2. 验证方法

```python
# 模态权重分析脚本
def analyze_modality_weights(model, dataloader):
    """分析不同类别样本的模态权重分布"""
    pose_weights_by_class = {0: [], 1: [], 2: []}
    img_weights_by_class = {0: [], 1: [], 2: []}
    
    model.eval()
    with torch.no_grad():
        for batch in dataloader:
            logits, modality_weights = model(batch['keypoint'], batch['imgs'])
            labels = batch['label']
            
            for i, label in enumerate(labels):
                pose_weights_by_class[label.item()].append(
                    modality_weights['pose_weight'][i].cpu().numpy()
                )
                img_weights_by_class[label.item()].append(
                    modality_weights['img_weight'][i].cpu().numpy()
                )
    
    # 打印统计结果
    for class_id, class_name in enumerate(['normal', 'cheat', 'no_hug']):
        print(f"{class_name}类样本:")
        print(f"  平均骨骼权重: {np.mean(pose_weights_by_class[class_id]):.3f}")
        print(f"  平均图像权重: {np.mean(img_weights_by_class[class_id]):.3f}")
```

### 3. 可视化分析

- **权重热力图**：不同类别样本的模态权重分布
- **权重变化曲线**：训练过程中模态权重的演化
- **特征激活图**：高权重模态的特征激活可视化

## 实验步骤

### 阶段1：组件实现（1-2天）

1. 实现 `AdaptiveMultiModalFusionNeck`
2. 实现 `AdaptiveTwoFusionHead`
3. 实现 `AdaptiveLoss`
4. 添加相关Hook

### 阶段2：配置与调试（1天）

1. 创建ExpI配置文件
2. 验证数据流通路
3. 小规模测试训练

### 阶段3：完整训练（3-4天）  

1. 启动100个epoch训练
2. 实时监控模态权重分布
3. 记录关键指标

### 阶段4：分析与优化（1-2天）

1. 分析模态权重合理性
2. 对比Baseline性能
3. 根据结果调优超参数

## 与现有实验的区别

| 特性 | Baseline实验4 | ExpI |
|------|---------------|------|
| 融合策略 | 固定cross_model | 自适应权重调整 |
| 特征利用 | 等权重融合 | 样本级动态权重 |
| 训练目标 | 单一分类损失 | 主损失+辅助约束 |
| 可解释性 | 黑盒融合 | 权重可视化分析 |
| 针对性 | 通用多模态 | 仰卧起坐场景定制 |

## 后续扩展方向

1. **ExpI_V2**：引入时序权重预测（35帧骨骼的帧级权重）
2. **ExpI_V3**：添加关节点级别的重要性预测（17个关节点的权重）
3. **ExpI_V4**：多粒度权重融合（帧级+关节级+模态级）

ExpI实验完全基于现有训练数据集，不需要额外的数据标注或预处理，仅通过模型架构创新来提升多模态特征的利用效率。
