# 方案A实验执行指南

## 🎯 实验目标

基于实验14的最佳配置（正常误判率0.94%），通过渐进式学习率优化进一步提升性能：
- **目标1**：正常误判率 < 0.5%
- **目标2**：异常检出率 > 0.98
- **目标3**：训练稳定性提升30%

## 📁 文件结构

```
configs/recognition/Multimodal/
├── multimodal_poseGCN-rgbR50_fusion.py                    # Baseline (实验4)
└── multimodal_poseGCN-rgbR50_fusion_expA_progressive_lr.py # 方案A配置

tools/
├── train_m2.py                    # 原始训练脚本
└── train_expA_progressive_lr.py   # 方案A专用训练脚本

docs/
├── 后续实验方案构思.md
├── 方案A_渐进式学习率优化详解.md
└── 方案A_实验执行指南.md         # 本文件
```

## 🚀 执行步骤

### 1. 环境检查
```bash
# 确认当前目录
cd /media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2_main0808

# 检查GPU状态
nvidia-smi

# 检查conda环境
conda info --envs
```

### 2. 启动训练
```bash
# 4卡训练（推荐）
python -m torch.distributed.launch \
    --nproc_per_node=4 \
    --master_port=29500 \
    tools/train_expA_progressive_lr.py \
    configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_expA_progressive_lr.py \
    --launcher pytorch

# 或者使用原始训练脚本
python -m torch.distributed.launch \
    --nproc_per_node=4 \
    --master_port=29500 \
    tools/train_m2.py \
    configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_expA_progressive_lr.py \
    --launcher pytorch
```

### 3. 监控训练
```bash
# 实时查看训练日志
tail -f work_dirs/pose_rgb_fusion/expA_progressive_lr_*/train.log

# 查看GPU使用情况
watch -n 1 nvidia-smi
```

## 📊 关键配置对比

| 配置项 | Baseline(实验4) | 实验14 | 方案A |
|--------|----------------|--------|-------|
| **预热策略** | 1阶段LinearLR | 2阶段LinearLR | **3阶段LinearLR** |
| **cls_head学习率** | lr×1.0 | lr×1.0 | **lr×1.2** |
| **重启策略** | 无 | 无 | **第50 epoch** |
| **dropout** | 0.5 | 0.6 | 0.6 |
| **weight_decay** | 0.0001 | 0.00015 | 0.00015 |
| **梯度裁剪** | max_norm=20 | max_norm=10 | max_norm=10 |

## 🔍 预期训练曲线特征

### 学习率变化
```
Epoch 0-3:   0.0000001 → 0.001     (极慢预热)
Epoch 3-8:   0.001 → 0.01          (加速预热)  
Epoch 8-50:  0.01 → 1e-6           (余弦退火)
Epoch 50:    重启标记点
Epoch 50-100: 继续余弦退火
```

### 性能指标预期
- **收敛速度**：前10个epoch更平滑
- **最终精度**：验证集top1 > 0.94
- **训练稳定性**：Gradient Norm方差减少
- **正常误判率**：< 0.5%

## 📈 实验监控要点

### 1. 学习率监控
- 检查3阶段预热是否按预期执行
- 观察第50 epoch的重启效果
- 记录每阶段的收敛速度

### 2. 性能指标
```bash
# 关键指标监控
grep "acc/top1" work_dirs/pose_rgb_fusion/expA_progressive_lr_*/train.log
grep "grad_norm" work_dirs/pose_rgb_fusion/expA_progressive_lr_*/train.log
```

### 3. 与基线对比
- 训练稳定性：梯度范数方差
- 收敛速度：达到0.9精度的epoch数
- 最终性能：测试集异常检出率和正常误判率

## 🛠️ 故障排除

### 常见问题
1. **OOM错误**：减少batch_size到2
2. **学习率异常**：检查param_scheduler配置
3. **收敛过慢**：确认cls_head学习率倍数设置

### 调试命令
```bash
# 检查配置文件语法
python -c "from mmengine.config import Config; Config.fromfile('configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_expA_progressive_lr.py')"

# 单GPU调试模式
python tools/train_expA_progressive_lr.py \
    configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_expA_progressive_lr.py \
    --work-dir work_dirs/debug_expA
```

## 📋 实验记录模板

```
实验名称：方案A - 渐进式学习率优化
开始时间：2025-08-12 XX:XX
配置文件：multimodal_poseGCN-rgbR50_fusion_expA_progressive_lr.py

训练环境：
- GPU: 4x [GPU型号]
- Batch Size: 4 per GPU
- 总Epochs: 100

关键观察：
- [ ] 3阶段预热执行正常
- [ ] 第50 epoch重启效果
- [ ] cls_head学习率1.2倍效果
- [ ] 训练稳定性改善

最终结果：
- 验证集最高精度：
- 测试集异常检出率：
- 正常误判率：
- 训练时长：

结论：
[记录实验结果和分析]
```

## 🎯 成功标准

**实验成功的判断标准：**
1. 正常误判率 < 0.5%（相比实验14的0.94%）
2. 异常检出率 > 0.98（相比实验14的0.9566）
3. 训练过程更稳定（梯度范数方差减少）
4. 收敛速度提升（更早达到高精度）

---
*实验执行时间：预计4-6小时（100 epochs）*  
*建议在GPU资源充足时执行*
