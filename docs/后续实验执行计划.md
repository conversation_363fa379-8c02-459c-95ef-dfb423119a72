# 后续实验执行计划

## 计划内容

```
---

后续实验构思（待研究，不改动Baseline文件，均以新配置文件落地）

- ExpC 交叉熵+标签平滑（稳态对照）
  - 新文件：multimodal_poseGCN-rgbR50_fusion_expC_ce_labelsmooth005.py
  - 关键项：CrossEntropyLoss(label_smoothing=0.05)；fusion_neck.lr_mult=0.8；cls_head.lr_mult=1.5；clip_grad=10；预热10ep
  - 目标：作为Focal对照，降低正常类误判并提高整体稳定性

- ExpD Focal重试（缓和欠拟合）
  - 新文件：multimodal_poseGCN-rgbR50_fusion_expD_focal_g1p5_ls005.py
  - 关键项：SGD lr=0.01；gamma=1.5；label_smoothing=0.05；fusion_neck.lr_mult=0.8；cls_head.lr_mult=1.5；预热start_factor=0.1
  - 目标：修正实验8欠拟合，兼顾稳定性与难例聚焦

- ExpE AMP+梯度累积（等效大batch）
  - 新文件：multimodal_poseGCN-rgbR50_fusion_expE_amp_accum2.py
  - 关键项：--amp；AmpOptimWrapper；accumulative_counts=2；AdamW；clip_grad=10
  - 目标：增大等效batch、平滑梯度

- ExpF 1Cycle（SGD）
  - 新文件：multimodal_poseGCN-rgbR50_fusion_expF_sgd_1cycle.py
  - 关键项：OneCycleLR；模块化max_lr（image_backbone较低、cls_head较高）
  - 目标：缩短收敛时间、降低早期振荡

- ExpG Dropout网格（注意力融合稳健性）
  - 新文件：multimodal_poseGCN-rgbR50_fusion_expG_dropout045.py / expG_dropout060.py
  - 关键项：仅调整fusion_neck与TwoFusionHead dropout（0.45/0.60）
  - 目标：平衡欠拟合/过拟合

- ExpH 多seed复现（投票/均值）
  - 方案：对ExpA用seed={3407,2025,7777}各跑一次
  - 目标：量化随机性影响，并提供推理期简单集成基线

- ExpI 注意力引导的模态选择
  - 新文件：multimodal_poseGCN-rgbR50_fusion_expI_adaptive_modality.py
  - 核心创新：样本级模态重要性预测，让模型自动学习对不同样本使用不同模态权重
  - 关键组件：AdaptiveMultiModalFusionNeck + AdaptiveLoss + 模态权重监控Hook
  - 目标：未抱头样本自动增加骨骼权重，外力作弊样本自动增加图像权重，正常误判率<1.0%
```

### 头脑风暴

```
头脑风暴：
方案一：训练稳定性优化系列
核心思路：通过改进训练策略和正则化技术，提升训练过程的稳定性
具体策略：
渐进式学习率预热：将现有5epoch预热延长至10epoch，使用更温和的start_factor（0.001→0.01→0.1）
自适应梯度裁剪：引入percentile-based梯度裁剪，动态调整max_norm阈值
随机深度训练：对融合模块引入stochastic depth，增强泛化能力
EMA权重平均：使用指数移动平均稳定模型权重更新
预期效果：减少训练抖动，提升收敛稳定性
方案二：多尺度时序建模增强
核心思路：针对仰卧起坐动作的时序特性，引入多尺度时序建模
具体策略：
多尺度骨骼采样：同时使用15、25、35帧的骨骼序列进行多尺度融合
时序注意力增强：在ST-GCN后添加时序self-attention模块
关键帧权重学习：自动学习起始帧、中间帧、结束帧的重要性权重
动作阶段感知：引入动作阶段标签辅助训练（躺平→起身→促膝）
预期效果：更好捕捉动作时序特征，提升对细微作弊行为的识别
方案三：困难样本挖掘与类别平衡
核心思路：针对正常误判问题，引入困难样本挖掘和智能类别平衡
具体策略：
在线困难样本挖掘：实时识别训练过程中的困难样本，增加其训练权重
类别感知损失：设计针对正常类的特殊损失项，降低误判风险
边界感知正则化：在特征空间中拉大类别间距离，缩小类内距离
伪标签学习：利用高置信度预测样本进行半监督学习
预期效果：显著降低正常误判率，提升整体检出精度
方案四：多模态融合策略优化
核心思路：改进现有融合策略，探索更有效的多模态交互方式
具体策略：
层次化融合：在多个层级进行特征融合，而非仅在最后一层
注意力门控机制：根据样本特点动态调整两种模态的贡献权重
对比学习增强：引入模态间对比学习，增强特征表达能力
跨模态知识蒸馏：让图像模态学习骨骼模态的运动先验知识
预期效果：提升多模态信息利用效率，增强模型判别能力
方案五：数据增强与预处理优化
核心思路：通过改进数据预处理和增强策略，提升模型泛化能力
具体策略：
时序数据增强：骨骼序列的时间扭曲、噪声注入、关节点遮挡
多视角图像合成：利用现有3张图像合成更多视角的训练样本
混合增强策略：MixUp、CutMix在多模态数据上的扩展应用
自适应归一化：根据不同学校场景自适应调整归一化参数
预期效果：增强模型对不同场景的适应能力，提升泛化性能
方案六：模型架构渐进式改进
核心思路：在保持现有架构基础上，进行渐进式的架构优化
具体策略：
特征维度优化：探索不同融合维度（256、384、512、768）的效果
残差连接增强：在融合模块中添加残差连接，缓解梯度消失
多头预测：使用多个分类头进行集成预测，提升稳定性
轻量化探索：在保持精度前提下，探索MobileNet等轻量化backbone
预期效果：在维持或提升精度的同时，优化模型效率
```

## 📋 执行概览

基于对当前项目状态的全面分析，本计划设计了6个核心实验方向，重点从训练策略优化入手，旨在将正常误判率降至1%以下，提升训练稳定性。

**当前最优基线**：实验13/14 - 异常检出率97.78%，正常误判率0.94%-2.36%

**目标**：正常误判率 < 1.0%，异常检出率 > 98%，训练稳定性提升30%

---

## 🧪 实验配置矩阵

| 实验ID | 实验名称 | 核心策略 | 预期改进点 | 配置文件 |
|--------|----------|----------|------------|----------|
| **ExpC** | 交叉熵+标签平滑（稳态对照） | CrossEntropyLoss + label_smoothing=0.05 | 降低正常误判，提供稳定对照 | `expC_ce_labelsmooth005.py` ✅ |
| **ExpD** | Focal重试（缓和欠拟合） | SGD + gamma=1.5 + label_smoothing | 修正实验8欠拟合，兼顾难例聚焦 | `expD_focal_g1p5_ls005.py` 🆕 |
| **ExpE** | AMP+梯度累积（等效大batch） | AmpOptimWrapper + accumulative_counts=2 | 增大等效batch，平滑梯度 | `expE_amp_accum2.py` 🆕 |
| **ExpF** | 1Cycle（SGD） | OneCycleLR + 模块化max_lr | 缩短收敛，降低早期振荡 | `expF_sgd_1cycle.py` 🆕 |
| **ExpG-0.45** | Dropout网格优化 | dropout=0.45（降低正则化） | 平衡欠拟合/过拟合 | `expG_dropout045.py` 🆕 |
| **ExpG-0.60** | Dropout网格优化 | dropout=0.60（增强正则化） | 提升泛化能力 | `expG_dropout060.py` 🆕 |

**符号说明**：✅ 已创建，🆕 新创建，⭐ 重点实验

---

## 🚀 执行策略

### 阶段一：核心对照实验（1-2周）

**目标**：建立新的性能基线，验证核心改进方向

#### 🔥 优先级1：ExpC（稳态对照）

```bash
# 启动ExpC实验
python tools/train_experiment_manager.py --experiment expC

# 关键监控指标
# - 正常误判率目标：< 1.5%
# - 训练稳定性：验证损失平滑度
# - 与实验13/14对比：标签平滑的效果
```

**预期结果**：提供稳定的交叉熵基线，正常误判率1.2%-1.8%

#### 🔥 优先级2：ExpD（Focal优化）

```bash
# 启动ExpD实验
python tools/train_experiment_manager.py --experiment expD

# 关键配置验证
# - gamma=1.5（降低难例聚焦强度）
# - SGD lr=0.01 + 分层学习率
# - 训练收敛性对比实验8
```

**预期结果**：修正实验8欠拟合，异常检出率>97%，正常误判率<1.5%

### 阶段二：训练策略优化（2-3周）

#### 🔥 优先级3：ExpE（AMP+梯度累积）

```bash
# 启动ExpE实验（需要--amp参数）
python tools/train_experiment_manager.py --experiment expE --amp

# 关键监控
# - 显存使用：预期减少20-30%
# - 等效batch_size=8的训练效果
# - 梯度稳定性：gradient norm曲线
```

**预期结果**：训练稳定性提升，内存效率提升，性能保持或略有提升

#### 🔥 优先级4：ExpF（1Cycle学习率）

```bash
# 启动ExpF实验
python tools/train_experiment_manager.py --experiment expF

# 收敛速度监控
# - 前30个epoch的快速上升期
# - 70个epoch的稳定下降期
# - 不同模块的学习率协调性
```

**预期结果**：50个epoch内达到收敛，减少训练时间40-50%

### 阶段三：精细调优（1-2周）

#### 🔥 优先级5：ExpG网格实验

```bash
# 并行运行两个dropout配置
python tools/train_experiment_manager.py --batch --experiments expG_045,expG_060

# 对比分析
# - dropout=0.45：模型表达能力 vs 欠拟合风险
# - dropout=0.60：泛化能力 vs 过拟合风险
# - 训练验证gap的变化
```

**预期结果**：找到最优dropout配置，进一步降低正常误判率

---

## 📊 监控指标体系

### 🎯 核心性能指标

| 指标类别 | 具体指标 | 目标值 | 监控频率 |
|----------|----------|--------|----------|
| **异常检测** | 异常检出率 | > 98.0% | 每个epoch |
| **正常保护** | 正常误判率 | < 1.0% | 每个epoch |
| **分类精度** | 3分类平均ACC | > 94% | 每个epoch |
| **二分类** | 2分类ACC | > 97% | 每个epoch |

### 📈 训练稳定性指标

| 指标类别 | 具体指标 | 期望趋势 | 关键epoch |
|----------|----------|----------|-----------|
| **收敛性** | 训练损失下降平滑度 | 单调下降，少抖动 | 1-30 |
| **泛化性** | 训练验证gap | < 5% | 50-100 |
| **梯度健康** | gradient norm | 稳定在2-15 | 全程 |
| **学习率协调** | 各模块参数更新幅度 | 协调一致 | 全程 |

### 🔍 模态融合指标（ExpI预备）

为后续ExpI实验（注意力引导模态选择）收集基线数据：

- **模态贡献度**：图像特征 vs 骨骼特征的融合权重分布
- **样本类别分析**：不同类别样本的特征激活模式
- **融合有效性**：跨模态注意力的收敛情况

---

## 🛠️ 技术实施细节

### 环境准备

```bash
# 确认环境
conda activate torch
cd /media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2_main0808

# 验证配置文件
python tools/train_experiment_manager.py --list

# 预检查GPU资源
nvidia-smi
```

### 批量执行方案

#### 方案A：顺序执行（推荐）

```bash
# 按优先级顺序执行
python tools/train_experiment_manager.py --experiment expC
python tools/train_experiment_manager.py --experiment expD  
python tools/train_experiment_manager.py --experiment expE --amp
python tools/train_experiment_manager.py --experiment expF
python tools/train_experiment_manager.py --batch --experiments expG_045,expG_060
```

#### 方案B：一键批量执行（高风险）

```bash
# 自动化批量执行（需要充足的计算资源和时间）
python tools/train_experiment_manager.py --batch --experiments expC,expD,expE,expF,expG_045,expG_060
```

### 结果收集脚本

```python
# 创建结果分析脚本
# tools/analyze_experiment_results.py（建议后续创建）

# 功能：
# 1. 自动收集各实验的最终指标
# 2. 生成对比报告和可视化
# 3. 识别最优配置组合
# 4. 为ExpI实验准备基线数据
```

---

## 📋 实验检查清单

### 🔍 实验前检查

- [ ] **环境确认**：conda activate torch，GPU可用
- [ ] **配置验证**：所有实验配置文件已创建且语法正确
- [ ] **数据路径**：训练/验证/测试数据路径正确
- [ ] **预训练模型**：STGCN和TSM预训练权重可访问
- [ ] **存储空间**：至少50GB可用存储（6个实验×约8GB/实验）

### 📊 实验中监控

- [ ] **训练启动**：前5个epoch正常启动，无OOM错误
- [ ] **指标记录**：tensorboard/wandb日志正常生成
- [ ] **检查点保存**：每个epoch的权重文件正常保存
- [ ] **异常中断**：设置训练中断后的自动恢复机制

### 📈 实验后分析

- [ ] **指标提取**：异常检出率、正常误判率、3分类ACC
- [ ] **曲线分析**：训练/验证损失和精度曲线
- [ ] **配置总结**：记录最优超参数组合
- [ ] **问题诊断**：记录训练过程中的异常现象

---

## 🎯 成功标准

### 🏆 最低成功标准

- **至少1个实验**：正常误判率 < 1.0%
- **训练稳定性**：validation loss抖动 < 0.05
- **实验完整性**：6个实验中至少4个成功完成

### 🌟 理想成功标准

- **性能突破**：正常误判率 < 0.8%，异常检出率 > 98.5%
- **训练效率**：收敛时间缩短30%，显存使用优化25%
- **方法验证**：找到最优训练策略组合，为ExpI实验奠定基础

### 📊 对比基线

所有实验结果将与当前最优的**实验13/14**进行对比：

- 基线指标：异常检出率97.78%，正常误判率0.94%-2.36%
- 基线配置：attention融合，2阶段预热，EMA，dropout=0.55

---

## 🔄 后续规划

### 短期（完成6个实验后）

1. **ExpI实验**：基于最优配置，实施注意力引导的模态选择
2. **多seed验证**：对最优配置进行多随机种子验证
3. **集成学习**：探索多个最优模型的集成策略

### 中期（数据增长后）

1. **数据增强**：基于更大数据集的重新训练
2. **架构搜索**：基于AutoML的架构优化
3. **部署优化**：模型量化和推理加速

---

## 📞 联系与支持

**执行负责人**：@Moss  
**项目路径**：`/media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2_main0808`  
**实验管理工具**：`tools/train_experiment_manager.py`  
**结果目录**：`work_dirs/pose_rgb_fusion_exp*`  

**紧急联系**：如遇到技术问题或资源不足，及时调整实验优先级和执行策略。

---

*本计划创建于2025-01-15，基于ReadMoss.md中的项目分析和实验需求设计。*
