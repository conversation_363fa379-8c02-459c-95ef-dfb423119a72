/* 基础样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f0f2f5;
    color: #333;
}

h1,
h2,
h3 {
    color: #1c1e21;
    margin: 0 0 15px 0;
}

/* 新的应用布局 */
.app-layout {
    display: flex;
    gap: 20px;
    height: calc(100vh - 80px);
    min-height: 600px;
}

/* 左侧面板 */
.left-panel {
    width: 350px;
    flex-shrink: 0;
}

.controls {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: 100%;
    overflow-y: auto;
}

/* 中央面板 */
.center-panel {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.canvas-container {
    flex-grow: 1;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
    min-height: 400px;
}

canvas {
    border: 1px solid #ccc;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    max-width: 100%;
    max-height: 100%;
}

/* 右侧参数面板 */
.right-panel {
    width: 320px;
    flex-shrink: 0;
}

#parameter-panel {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: 100%;
    overflow: hidden;
    transition: width 0.3s ease;
}

#parameter-panel.collapsed {
    width: 60px;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #dddfe2;
    background: #f8f9fa;
}

.panel-header h3 {
    margin: 0;
    font-size: 16px;
}

.toggle-btn {
    padding: 5px 10px;
    border: 1px solid #dddfe2;
    background: #fff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.toggle-btn:hover {
    background: #f0f2f5;
}

.panel-content {
    padding: 20px;
    height: calc(100% - 70px);
    overflow-y: auto;
}

#parameter-panel.collapsed .panel-content {
    display: none;
}

#parameter-panel.collapsed .panel-header h3 {
    display: none;
}

/* 参数组 */
.param-group {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e4e6eb;
}

.param-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.param-group h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #1c1e21;
}

.param-item {
    margin-bottom: 15px;
}

.param-item label {
    display: block;
    margin-bottom: 5px;
    font-size: 13px;
    font-weight: 500;
    color: #65676b;
}

/* 滑块容器 */
.slider-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.slider-container input[type="range"] {
    flex-grow: 1;
    height: 6px;
    border-radius: 3px;
    background: #dddfe2;
    outline: none;
    -webkit-appearance: none;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #1877f2;
    cursor: pointer;
}

.slider-container input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #1877f2;
    cursor: pointer;
    border: none;
}

.param-input {
    width: 60px;
    padding: 4px 8px;
    border: 1px solid #dddfe2;
    border-radius: 4px;
    font-size: 12px;
    text-align: center;
}

/* 选择框 */
select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dddfe2;
    border-radius: 4px;
    background: #fff;
    font-size: 13px;
}

/* 复选框 */
input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin: 0;
}

/* 参数操作按钮 */
.param-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.param-actions .action-btn {
    flex: 1;
    padding: 8px 12px;
    font-size: 12px;
    margin-top: 0;
}

/* 预览控制区域 */
#preview-controls {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 15px;
    margin-top: 15px;
}

.preview-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.preview-actions .action-btn {
    width: auto;
    padding: 8px 16px;
    font-size: 13px;
    margin-top: 0;
}

.preview-status {
    padding: 8px 12px;
    background: #f0f2f5;
    border-radius: 4px;
    text-align: center;
}

#preview-status-text {
    font-size: 13px;
    color: #65676b;
    font-weight: 500;
}

.preview-status.loading #preview-status-text {
    color: #1877f2;
}

.preview-status.error #preview-status-text {
    color: #fa383e;
}

.preview-status.success #preview-status-text {
    color: #42b72a;
}

/* 蒙版预览 */
#mask-preview-container {
    display: none;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
    text-align: center;
}

#mask-preview-image {
    max-width: 100%;
    max-height: 150px;
    border: 1px solid #dddfe2;
    border-radius: 4px;
    margin-top: 10px;
}

/* 模式选择 */
.mode-selection {
    display: flex;
    margin-bottom: 20px;
}

.mode-btn {
    flex-grow: 1;
    padding: 10px;
    border: 1px solid #dddfe2;
    background-color: #f0f2f5;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.mode-btn.active {
    background-color: #1877f2;
    color: white;
    border-color: #1877f2;
}

.mode-btn:first-child {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
}

.mode-btn:last-child {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
}

.mode-btn:hover:not(.active) {
    background-color: #e4e6eb;
}

/* 图片列表 */
.image-list {
    list-style: none;
    padding: 0;
    margin: 10px 0 20px 0;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dddfe2;
    border-radius: 6px;
    background: #fff;
}

.image-list li {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f2f5;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: background-color 0.2s ease;
    font-size: 13px;
}

.image-list li:last-child {
    border-bottom: none;
}

.image-list li:hover {
    background-color: #f0f2f5;
}

.image-list li.selected {
    background-color: #e7f3ff;
    color: #1877f2;
    font-weight: 600;
}

/* 操作按钮 */
.action-btn {
    width: 100%;
    padding: 12px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 6px;
    border: none;
    background-color: #42b72a;
    color: white;
    cursor: pointer;
    margin-top: 10px;
    transition: background-color 0.2s ease;
}

.action-btn:hover {
    background-color: #36a420;
}

.action-btn:disabled {
    background-color: #bcc0c4;
    cursor: not-allowed;
}

.action-btn.secondary {
    background-color: #e4e6eb;
    color: #1c1e21;
}

.action-btn.secondary:hover {
    background-color: #d8dadf;
}

.action-btn.reject {
    background-color: #fa383e;
}

.action-btn.reject:hover {
    background-color: #e32b31;
}

/* 说明文字 */
.instructions {
    margin-top: 20px;
    padding: 15px;
    background: #e7f3ff;
    border-radius: 6px;
    border: 1px solid #cce0ff;
    font-size: 13px;
    line-height: 1.4;
}

#progress-info {
    padding: 10px;
    background: #f0f2f5;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 10px;
    text-align: center;
    color: #65676b;
}

/* 模态对话框 */
#modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
}

#confirmation-panel {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    text-align: center;
    width: auto;
    max-width: 80vw;
    max-height: 80vh;
    padding: 30px;
    overflow: auto;
}

#confirmation-panel h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
}

#result-image {
    max-width: 100%;
    max-height: 50vh;
    border: 1px solid #dddfe2;
    border-radius: 8px;
    margin: 0 0 25px 0;
    cursor: zoom-in;
    transition: transform 0.2s ease;
}

#result-image:hover {
    transform: scale(1.02);
}

.confirmation-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.confirmation-buttons .action-btn {
    width: auto;
    padding: 12px 24px;
    margin-top: 0;
}

/* 工具提示 */
[title] {
    position: relative;
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .app-layout {
        flex-direction: column;
        height: auto;
    }

    .left-panel,
    .right-panel {
        width: 100%;
    }

    .controls {
        height: auto;
    }

    #parameter-panel {
        height: auto;
    }

    #parameter-panel.collapsed {
        width: 100%;
        height: 60px;
    }

    .panel-content {
        height: auto;
        max-height: 400px;
    }
}

@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .app-layout {
        gap: 10px;
    }

    .canvas-container {
        padding: 10px;
    }

    #confirmation-panel {
        padding: 20px;
        margin: 20px;
    }

    .confirmation-buttons {
        flex-direction: column;
    }

    .confirmation-buttons .action-btn {
        width: 100%;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f0f2f5;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #bcc0c4;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #8a8d91;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #1877f2;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 焦点状态 */
input:focus,
select:focus,
button:focus {
    outline: 2px solid #1877f2;
    outline-offset: 2px;
}

/* 选中状态动画 */
.image-list li,
.mode-btn,
.action-btn {
    transition: all 0.2s ease;
}

/* 错误状态 */
.error {
    border-color: #fa383e !important;
    background-color: #fff5f5 !important;
}

.error-message {
    color: #fa383e;
    font-size: 12px;
    margin-top: 5px;
}

/* 路径配置样式 */
.path-config-section {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.path-config-section h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
}

.path-input-group {
    margin-bottom: 15px;
}

.path-input-group:last-child {
    margin-bottom: 0;
}

.path-input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
    font-size: 14px;
}

.path-input-container {
    display: flex;
    gap: 8px;
    align-items: center;
}

.path-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    background-color: #fff;
}

.path-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.path-input:read-only {
    background-color: #e9ecef;
    color: #6c757d;
}

.browse-btn {
    padding: 8px 16px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    white-space: nowrap;
    transition: background-color 0.2s;
}

.browse-btn:hover {
    background-color: #0056b3;
}

.path-info {
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
    display: block;
    margin-top: 4px;
}

/* 目录浏览器模态对话框 */
#directory-browser-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

#directory-browser-panel {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 500px;
    max-width: 90vw;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
}

#directory-browser-panel h3 {
    margin: 0;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.current-path-display {
    padding: 10px 20px;
    background-color: #e9ecef;
    border-bottom: 1px solid #dee2e6;
    font-family: monospace;
    font-size: 14px;
}

.directory-list-container {
    flex: 1;
    overflow-y: auto;
    min-height: 200px;
    max-height: 400px;
}

.directory-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.directory-list li {
    padding: 10px 20px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: background-color 0.2s;
}

.directory-list li:hover {
    background-color: #f8f9fa;
}

.directory-list li:before {
    content: "📁";
    margin-right: 8px;
    font-size: 16px;
}

.directory-list li.parent-dir:before {
    content: "⬆️";
}

.browser-buttons {
    padding: 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* 当前路径显示 */
.current-path-display {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 10px;
    font-family: monospace;
    font-size: 13px;
    color: #495057;
}

/* 文件项目显示样式 */
.file-item {
    border-radius: 6px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.file-item:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}

.file-item.selected {
    background-color: #e3f2fd;
    border-left: 4px solid #1976d2;
    font-weight: 500;
}

.file-item-content {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 2px 0;
}

.file-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 500;
    color: #333;
    font-size: 14px;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-type {
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
}

/* 文件夹特殊样式 */
.file-item.directory:hover .file-name {
    color: #1976d2;
}

.file-item.directory .file-icon {
    color: #ffa726;
}

/* 图片文件特殊样式 */
.file-item.image.selected .file-name {
    color: #1976d2;
}

.file-item.image .file-icon {
    color: #4caf50;
}

/* 改进图片列表样式 */
.image-list li {
    border-radius: 6px;
    transition: all 0.2s ease;
    margin-bottom: 2px;
}

.image-list li:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}

.image-list li.selected {
    background-color: #e3f2fd;
    border-left: 4px solid #1976d2;
    font-weight: 500;
}

/* 快捷键说明样式 */
.hotkeys {
    margin-top: 15px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.hotkeys h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
}

.hotkeys ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.hotkeys li {
    margin-bottom: 6px;
    font-size: 13px;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 8px;
}

.hotkeys li:last-child {
    margin-bottom: 0;
}

.hotkeys kbd {
    background-color: #fff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 2px 6px;
    font-family: monospace;
    font-size: 11px;
    color: #374151;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    min-width: 45px;
    text-align: center;
}