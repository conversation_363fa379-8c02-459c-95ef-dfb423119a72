document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('main-canvas');
    const ctx = canvas.getContext('2d');

    const modeMaskBtn = document.getElementById('mode-mask-btn');
    const modeGenBtn = document.getElementById('mode-gen-btn');
    const maskCreationPanel = document.getElementById('mask-creation-panel');
    const dataGenerationPanel = document.getElementById('data-generation-panel');
    const sourceImageList = document.getElementById('source-image-list');
    const saveMaskBtn = document.getElementById('save-mask-btn');
    const instructionText = document.getElementById('instruction-text');

    let currentMode = 'mask-creation';
    let currentImage = null;
    let points = [];
    let currentImagePath = '';

    // --- 画布变换状态 ---
    let scale = 1.0;
    let originX = 0;
    let originY = 0;
    let isDragging = false;
    let lastDragX = 0;
    let lastDragY = 0;

    // --- 模式切换 ---
    modeMaskBtn.addEventListener('click', () => switchMode('mask-creation'));
    modeGenBtn.addEventListener('click', () => switchMode('data-generation'));

    function switchMode(mode) {
        currentMode = mode;
        points = [];
        currentImage = null;
        resetCanvasTransform();

        if (mode === 'mask-creation') {
            modeMaskBtn.classList.add('active');
            modeGenBtn.classList.remove('active');
            maskCreationPanel.classList.remove('hidden');
            dataGenerationPanel.classList.add('hidden');
            instructionText.textContent = '请在左侧列表中选择一张图片开始创建蒙版。';
            loadSourceImages();
        } else {
            // --- 模式二：数据生成 ---
            modeMaskBtn.classList.remove('active');
            modeGenBtn.classList.add('active');
            maskCreationPanel.classList.add('hidden');
            dataGenerationPanel.classList.remove('hidden');
            instructionText.textContent = '请在下方列表中选择一个工具蒙版，然后开始处理样本。';
            initDataGeneration();
        }
    }

    // --- 模式一：蒙版创建 ---
    async function loadSourceImages() {
        const response = await fetch('/api/source_images?t=' + new Date().getTime());
        const images = await response.json();
        sourceImageList.innerHTML = '';
        images.forEach(imgData => {
            const li = document.createElement('li');
            // 新的数据结构：{id: "img_xxx", name: "filename.jpg", category: "source"}
            li.textContent = imgData.name || imgData.id;
            li.dataset.id = imgData.id;
            li.title = imgData.name || imgData.id;
            li.addEventListener('click', () => loadImageToCanvas(imgData.id));
            sourceImageList.appendChild(li);
        });
    }

    function loadImageToCanvas(imgId) {
        currentImagePath = imgId;  // 现在存储的是ID而不是路径
        points = [];
        const img = new Image();
        img.src = `/image/${imgId}`;
        img.onload = () => {
            // 处理高DPI，避免点击偏移
            const ratio = window.devicePixelRatio || 1;
            const cw = canvas.parentElement.clientWidth;
            const ch = canvas.parentElement.clientHeight;
            canvas.width = Math.floor(cw * ratio);
            canvas.height = Math.floor(ch * ratio);
            canvas.style.width = cw + 'px';
            canvas.style.height = ch + 'px';
            ctx.setTransform(1, 0, 0, 1, 0, 0);
            ctx.scale(ratio, ratio);

            currentImage = img;

            // 初始缩放以适应画布（以CSS像素为基准）
            const hRatio = cw / img.width;
            const vRatio = ch / img.height;
            scale = Math.min(hRatio, vRatio, 1);

            // 居中显示
            originX = (cw - img.width * scale) / 2;
            originY = (ch - img.height * scale) / 2;

            redraw();
            instructionText.textContent = '操作提示: Ctrl+滚轮缩放, 拖动平移, Ctrl+Z撤销。';
        };
    }

    function resetCanvasTransform() {
        scale = 1.0;
        originX = 0;
        originY = 0;
        ctx.clearRect(0, 0, canvas.width, canvas.height);
    }

    function redraw() {
        if (!currentImage) return;

        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 应用变换并绘制图像
        ctx.save();
        ctx.translate(originX, originY);
        ctx.scale(scale, scale);
        ctx.drawImage(currentImage, 0, 0);
        ctx.restore();

        // 绘制点和线
        ctx.save();
        ctx.translate(originX, originY);
        ctx.scale(scale, scale);

        if (currentMode === 'mask-creation') {
            drawPolygon();
        } else if (currentMode === 'data-generation') {
            drawKeypoints();
        }
        ctx.restore();
    }

    // 更新原有的drawPolygon和新增drawKeypoints以在变换后坐标系中绘制
    function drawPolygon() {
        if (points.length === 0) return;
        ctx.strokeStyle = '#00ff00';
        ctx.lineWidth = 2 / scale; // 线宽反比于缩放，保持视觉一致
        ctx.beginPath();
        // 转换点坐标
        ctx.moveTo(points[0][0], points[0][1]);
        for (let i = 1; i < points.length; i++) {
            ctx.lineTo(points[i][0], points[i][1]);
        }
        if (points.length > 2) {
            ctx.closePath();
        }
        ctx.stroke();

        // 新增：在每个点击点绘制小圆点以增强可视化
        ctx.fillStyle = '#00ff00';
        const radius = 4 / scale;
        for (let i = 0; i < points.length; i++) {
            const p = points[i];
            ctx.beginPath();
            ctx.arc(p[0], p[1], radius, 0, 2 * Math.PI);
            ctx.fill();
        }
    }

    function drawKeypoints() {
        points.forEach(p => {
            ctx.fillStyle = '#ff0000';
            ctx.beginPath();
            ctx.arc(p[0], p[1], 5 / scale, 0, 2 * Math.PI); // 半径反比于缩放
            ctx.fill();
        });
    }

    // 归一化点至图像边界，防止越界引入误差
    function clampPointToImage(px, py) {
        const x = Math.max(0, Math.min(currentImage.width - 1, px));
        const y = Math.max(0, Math.min(currentImage.height - 1, py));
        return [x, y];
    }

    canvas.addEventListener('click', (e) => {
        if (!currentImage) return;
        e.preventDefault();

        // --- 坐标转换（优先使用 offsetX/offsetY，退化到 getBoundingClientRect）---
        let cssX, cssY;
        if (typeof e.offsetX === 'number' && typeof e.offsetY === 'number') {
            cssX = e.offsetX;
            cssY = e.offsetY;
        } else {
            const rect = canvas.getBoundingClientRect();
            cssX = e.clientX - rect.left;
            cssY = e.clientY - rect.top;
        }
        const imageX = (cssX - originX) / scale;
        const imageY = (cssY - originY) / scale;

        if (currentMode === 'mask-creation') {
            const clamped = clampPointToImage(imageX, imageY);
            points.push(clamped);
        } else if (currentMode === 'data-generation' && points.length < 2) {
            points.push([imageX, imageY]);
            if (points.length === 2) {
                instructionText.textContent = '两点已选择。点击“处理下一个”按钮生成样本。';
            }
        }

        redraw();
    });

    // --- 缩放事件 ---
    canvas.addEventListener('wheel', (e) => {
        if (!currentImage || !e.ctrlKey) return;
        e.preventDefault();

        const rect = canvas.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        const zoomFactor = e.deltaY < 0 ? 1.1 : 0.9;

        const newScale = scale * zoomFactor;

        // 更新原点以保持鼠标位置为缩放中心
        originX = mouseX - (mouseX - originX) * zoomFactor;
        originY = mouseY - (mouseY - originY) * zoomFactor;
        scale = newScale;

        redraw();
    });

    // --- 平移事件 ---
    canvas.addEventListener('mousedown', (e) => {
        if (!currentImage) return;
        // 所有模式下都需要按住 Ctrl 才允许平移，避免误触导致点击偏移
        if (!e.ctrlKey) return;
        isDragging = true;
        lastDragX = e.clientX;
        lastDragY = e.clientY;
        canvas.style.cursor = 'grabbing';
    });

    canvas.addEventListener('mousemove', (e) => {
        if (!isDragging || !currentImage) return;
        const dx = e.clientX - lastDragX;
        const dy = e.clientY - lastDragY;
        originX += dx;
        originY += dy;
        lastDragX = e.clientX;
        lastDragY = e.clientY;
        redraw();
    });

    canvas.addEventListener('mouseup', () => {
        isDragging = false;
        canvas.style.cursor = 'default';
    });
    canvas.addEventListener('mouseleave', () => {
        isDragging = false;
        canvas.style.cursor = 'default';
    });

    // --- 撤销事件 ---
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.key === 'z') {
            e.preventDefault();
            if (points.length > 0) {
                points.pop();
                redraw();
            }
        }
    });

    saveMaskBtn.addEventListener('click', async () => {
        if (points.length < 3) {
            alert('请至少选择3个点来形成一个多边形。');
            return;
        }

        const response = await fetch('/api/create_mask', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ image_path: currentImagePath, points: points })
        });
        const result = await response.json();

        if (result.status === 'success') {
            alert('蒙版创建成功！');
            points = [];
            redraw();
            // 关键：在这里调用函数以刷新模式二中的蒙版列表
            if (currentMode === 'mask-creation') {
                loadToolMasks();
            }
        } else {
            alert(`错误: ${result.message}`);
        }
    });

    // --- 模式二：数据生成 ---
    // 统一并修正所有DOM元素引用
    const toolMasksList = document.getElementById('tool-mask-list');
    const normalSamplesList = document.getElementById('normal-sample-list');
    const nextSampleBtn = document.getElementById('next-sample-btn');
    const progressInfo = document.getElementById('progress-info');
    const modalOverlay = document.getElementById('modal-overlay');
    const confirmationPanel = document.getElementById('confirmation-panel');
    const resultImage = document.getElementById('result-image');
    const confirmBtn = document.getElementById('confirm-btn');
    const rejectBtn = document.getElementById('reject-btn');
    const maskPreviewContainer = document.getElementById('mask-preview-container');
    const maskPreviewImage = document.getElementById('mask-preview-image');

    let normalSamples = [];
    let toolMasks = [];
    let currentSampleIndex = 0;
    let currentImageIndex = 0;
    let selectedToolMask = null;

    // 提取出的可重用函数
    async function loadToolMasks() {
        const masksResponse = await fetch('/api/tool_masks?t=' + new Date().getTime());
        toolMasks = await masksResponse.json();
        toolMasksList.innerHTML = '';
        toolMasks.forEach(mask => {
            const li = document.createElement('li');
            // 新的数据结构：{id: "img_xxx", name: "filename.png"}
            const maskId = mask.id;
            li.textContent = mask.name;
            li.title = mask.name;
            li.dataset.id = maskId;

            li.addEventListener('click', () => {
                document.querySelectorAll('#tool-mask-list li').forEach(item => {
                    item.classList.remove('selected');
                });
                li.classList.add('selected');
                selectedToolMask = maskId;  // 现在存储ID而不是路径

                maskPreviewImage.src = `/image/${maskId}`;
                maskPreviewContainer.style.display = 'block';
                instructionText.textContent = `已选择蒙版: ${mask.name}。现在可以开始处理样本。`;
            });

            toolMasksList.appendChild(li);
        });
    }

    async function initDataGeneration() {
        // 加载蒙版
        await loadToolMasks();

        // 加载待处理样本
        const samplesResponse = await fetch('/api/normal_samples?t=' + new Date().getTime());
        normalSamples = await samplesResponse.json();

        // --- 修正并补全：渲染待处理样本列表 ---
        normalSamplesList.innerHTML = '';
        normalSamples.forEach((sample, index) => {
            const li = document.createElement('li');
            li.textContent = sample.sample_name;
            li.title = sample.sample_name;
            li.addEventListener('click', () => {
                currentSampleIndex = index;
                currentImageIndex = 0;
                displayCurrentSampleInfo();
                loadNextImageForGeneration();
            });
            normalSamplesList.appendChild(li);
        });
        // --- 修正结束 ---

        currentSampleIndex = 0;
        currentImageIndex = 0;
        displayCurrentSampleInfo();
        loadNextImageForGeneration();
    }

    function displayCurrentSampleInfo() {
        if (currentSampleIndex >= normalSamples.length) {
            progressInfo.textContent = '所有样本处理完毕！';
            return;
        }
        const sample = normalSamples[currentSampleIndex];
        progressInfo.textContent = `样本: ${sample.sample_name} (${currentSampleIndex + 1}/${normalSamples.length}) | 图像: ${currentImageIndex + 1}/3`;
    }

    function loadNextImageForGeneration() {
        if (currentSampleIndex >= normalSamples.length) {
            alert('所有样本已处理完毕！');
            switchMode('mask-creation'); // 重置回模式一
            return;
        }

        const sample = normalSamples[currentSampleIndex];
        const imgData = sample.images[currentImageIndex];
        // 新的数据结构：{id: "img_xxx", name: "filename.jpg"}
        loadImageToCanvas(imgData.id);
        instructionText.textContent = `请在画布上依次点击两个手腕的位置。`;
    }

    nextSampleBtn.addEventListener('click', async () => {
        if (currentMode !== 'data-generation' || points.length !== 2) {
            alert('请先在图片上选择两个点。');
            return;
        }
        if (!selectedToolMask) {
            alert('请先从列表中选择一个工具蒙版。');
            return;
        }

        const sample = normalSamples[currentSampleIndex];
        const imgData = sample.images[currentImageIndex];

        // 发送请求
        const response = await fetch('/api/generate_sample', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                image_path: imgData.id,  // 现在发送ID而不是路径
                points: points,
                tool_mask: selectedToolMask  // 也是ID
            })
        });
        const result = await response.json();

        if (result.status !== 'success') {
            alert(`生成失败: ${result.message}`);
            return;
        }

        // --- 新的确认流程 ---
        // 显示结果图
        resultImage.src = `/image/${result.generated_image_id}?t=${new Date().getTime()}`; // 使用新的ID字段

        // 切换UI状态
        nextSampleBtn.classList.add('hidden');
        modalOverlay.style.display = 'flex';
        instructionText.textContent = '请审核生成的结果。点击图片可查看大图。';
    });

    confirmBtn.addEventListener('click', () => {
        // 推进到下一个
        currentImageIndex++;
        if (currentImageIndex >= 3) {
            currentImageIndex = 0;
            currentSampleIndex++;
        }

        // 切换UI状态
        modalOverlay.style.display = 'none';
        nextSampleBtn.classList.remove('hidden');
        points = [];

        // 加载下一张图
        displayCurrentSampleInfo();
        loadNextImageForGeneration();
    });

    rejectBtn.addEventListener('click', () => {
        // 不推进，只重置当前状态
        points = [];

        // 切换UI状态
        modalOverlay.style.display = 'none';
        nextSampleBtn.classList.remove('hidden');

        // 重新加载当前图像以供标注
        redraw();
        instructionText.textContent = `请对当前图片重新进行标注。`;
    });

    resultImage.addEventListener('click', () => {
        if (resultImage.src) {
            window.open(resultImage.src, '_blank');
        }
    });

    modalOverlay.addEventListener('click', (e) => {
        // 如果点击的是背景覆盖层本身，而不是它内部的面板，则触发拒绝/重做
        if (e.target === modalOverlay) {
            rejectBtn.click();
        }
    });

    // --- 初始化 ---
    loadSourceImages();
});
