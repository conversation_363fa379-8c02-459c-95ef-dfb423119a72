/**
 * 主画布实时预览引擎
 * 直接在主画布上显示预览效果，替代小预览窗口
 */
class MainCanvasPreview {
    constructor(canvas, coordinateManager) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.coordinateManager = coordinateManager;

        // 预览状态
        this.isEnabled = false;
        this.isPreviewMode = false;
        this.originalImage = null;
        this.previewImage = null;
        this.keypoints = [];
        this.selectedToolMask = null;
        this.currentParameters = {};

        // 防抖计时器
        this.debounceTimer = null;
        this.debounceDelay = 300; // 毫秒，稍长一些避免频繁请求后端

        // 加载状态
        this.isLoading = false;

        // 事件回调
        this.onPreviewUpdate = null;
        this.onPreviewError = null;
    }

    /**
     * 设置预览数据
     * @param {HTMLImageElement} originalImage - 原始图像
     * @param {string} toolMaskPath - 工具蒙版路径
     * @param {Array} keypoints - 关键点坐标 [x1, y1, x2, y2]
     * @param {string} imagePath - 图像路径
     */
    setPreviewData(originalImage, toolMaskPath, keypoints, imagePath) {
        this.originalImage = originalImage;
        this.selectedToolMask = toolMaskPath;
        this.keypoints = keypoints;
        this.currentImagePath = imagePath;

        if (this.isEnabled && keypoints && keypoints.length >= 4) {
            this.updatePreview();
        }
    }

    /**
     * 更新预览参数并刷新预览
     * @param {Object} parameters - 融合参数
     */
    updatePreview(parameters = null) {
        if (!this.isEnabled ||
            !this.originalImage ||
            !this.selectedToolMask ||
            !this.keypoints ||
            this.keypoints.length < 4) {
            return;
        }

        if (parameters) {
            this.currentParameters = { ...parameters };
        }

        // 防抖处理
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }

        this.debounceTimer = setTimeout(() => {
            this.requestPreviewFromBackend();
        }, this.debounceDelay);
    }

    /**
     * 向后端请求预览图像
     */
    async requestPreviewFromBackend() {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoadingState();

        try {
            const response = await fetch('/api/realtime_preview', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    image_path: this.currentImagePath,
                    points: [
                        [this.keypoints[0], this.keypoints[1]],
                        [this.keypoints[2], this.keypoints[3]]
                    ],
                    tool_mask: this.selectedToolMask,
                    parameters: this.getAPIParameters()
                })
            });

            const result = await response.json();

            if (result.status === 'success') {
                await this.loadPreviewImage(result.preview_image);
                this.showPreview();
            } else {
                console.error('预览生成失败:', result.message);
                this.showError(result.message);
            }
        } catch (error) {
            console.error('预览请求失败:', error);
            this.showError('网络请求失败');
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * 加载预览图像
     * @param {string} dataUrl - base64编码的图像数据
     */
    loadPreviewImage(dataUrl) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                this.previewImage = img;
                resolve(img);
            };
            img.onerror = reject;
            img.src = dataUrl;
        });
    }

    /**
     * 显示预览图像
     */
    showPreview() {
        if (!this.previewImage) return;

        this.isPreviewMode = true;
        this.redrawCanvas();

        if (this.onPreviewUpdate) {
            this.onPreviewUpdate(this.previewImage);
        }
    }

    /**
     * 显示加载状态
     */
    showLoadingState() {
        // 在原图上叠加半透明加载指示
        this.redrawCanvas();

        this.coordinateManager.applyTransformToContext();

        // 绘制半透明遮罩
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
        this.ctx.fillRect(0, 0, this.originalImage.width, this.originalImage.height);

        // 绘制加载文字
        this.ctx.fillStyle = '#1877f2';
        this.ctx.font = '24px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
            '生成预览中...',
            this.originalImage.width / 2,
            this.originalImage.height / 2
        );

        this.coordinateManager.restoreContext();
    }

    /**
     * 显示错误状态
     * @param {string} message - 错误信息
     */
    showError(message) {
        this.redrawCanvas();

        this.coordinateManager.applyTransformToContext();

        // 绘制错误提示
        this.ctx.fillStyle = 'rgba(255, 107, 107, 0.8)';
        this.ctx.fillRect(0, 0, this.originalImage.width, this.originalImage.height);

        this.ctx.fillStyle = 'white';
        this.ctx.font = '18px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
            '预览失败',
            this.originalImage.width / 2,
            this.originalImage.height / 2 - 10
        );
        this.ctx.font = '14px Arial';
        this.ctx.fillText(
            message,
            this.originalImage.width / 2,
            this.originalImage.height / 2 + 15
        );

        this.coordinateManager.restoreContext();

        if (this.onPreviewError) {
            this.onPreviewError(message);
        }
    }

    /**
     * 重绘画布
     */
    redrawCanvas() {
        if (!this.originalImage) return;

        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 应用坐标变换
        this.coordinateManager.applyTransformToContext();

        // 绘制图像（原图或预览图）
        const imageToShow = (this.isPreviewMode && this.previewImage) ?
            this.previewImage : this.originalImage;
        this.ctx.drawImage(imageToShow, 0, 0);

        this.coordinateManager.restoreContext();
    }

    /**
     * 启用/禁用预览
     * @param {boolean} enabled - 是否启用
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;

        if (!enabled) {
            this.hidePreview();
        }
    }

    /**
     * 隐藏预览，显示原图
     */
    hidePreview() {
        this.isPreviewMode = false;
        this.previewImage = null;
        this.redrawCanvas();
    }

    /**
     * 切换预览显示
     */
    togglePreview() {
        if (this.isPreviewMode) {
            this.hidePreview();
        } else if (this.previewImage) {
            this.showPreview();
        }
    }

    /**
     * 获取API参数格式
     */
    getAPIParameters() {
        return {
            blend_strength: this.currentParameters.blendStrength || 0.6,
            fusion_mode: this.currentParameters.fusionMode || 'NORMAL_CLONE',
            seamless_weight: this.currentParameters.seamlessWeight || 0.6,
            mask_opacity: this.currentParameters.maskOpacity || 1.0,
            brightness_adjust: this.currentParameters.brightnessAdjust || 0,
            contrast_adjust: this.currentParameters.contrastAdjust || 1.0
        };
    }

    /**
     * 设置防抖延迟
     * @param {number} delay - 延迟时间（毫秒）
     */
    setDebounceDelay(delay) {
        this.debounceDelay = delay;
    }

    /**
     * 获取当前是否在预览模式
     */
    isInPreviewMode() {
        return this.isPreviewMode;
    }

    /**
     * 获取预览图像
     */
    getPreviewImage() {
        return this.previewImage;
    }

    /**
     * 清空预览数据
     */
    clear() {
        this.originalImage = null;
        this.previewImage = null;
        this.keypoints = [];
        this.selectedToolMask = null;
        this.currentParameters = {};
        this.isPreviewMode = false;
        this.isEnabled = false;

        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }
    }

    /**
     * 设置预览更新回调
     * @param {Function} callback - 回调函数
     */
    setPreviewUpdateCallback(callback) {
        this.onPreviewUpdate = callback;
    }

    /**
     * 设置预览错误回调
     * @param {Function} callback - 回调函数
     */
    setPreviewErrorCallback(callback) {
        this.onPreviewError = callback;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MainCanvasPreview;
} else {
    window.MainCanvasPreview = MainCanvasPreview;
}

