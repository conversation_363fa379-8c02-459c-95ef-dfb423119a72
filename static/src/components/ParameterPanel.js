/**
 * 参数控制面板组件
 * 提供图像融合和颜色调整的可视化控制界面
 */
class ParameterPanel {
    constructor() {
        this.panel = document.getElementById('parameter-panel');
        this.toggleBtn = document.getElementById('panel-toggle-btn');
        this.isCollapsed = true;
        
        // 参数默认值
        this.parameters = {
            blendStrength: 0.6,
            fusionMode: 'NORMAL_CLONE',
            seamlessWeight: 0.6,
            maskOpacity: 1.0,
            brightnessAdjust: 0,
            contrastAdjust: 1.0,
            realtimePreview: true,
            previewSize: 200
        };
        
        // 事件回调
        this.onParameterChange = null;
        this.onPreviewToggle = null;
        
        this.initializeElements();
        this.bindEvents();
    }
    
    /**
     * 初始化所有参数控制元素
     */
    initializeElements() {
        // 融合参数
        this.blendStrengthSlider = document.getElementById('blend-strength');
        this.blendStrengthInput = document.getElementById('blend-strength-input');
        this.fusionModeSelect = document.getElementById('fusion-mode');
        this.seamlessWeightSlider = document.getElementById('seamless-weight');
        this.seamlessWeightInput = document.getElementById('seamless-weight-input');
        
        // 颜色调整
        this.maskOpacitySlider = document.getElementById('mask-opacity');
        this.maskOpacityInput = document.getElementById('mask-opacity-input');
        this.brightnessSlider = document.getElementById('brightness-adjust');
        this.brightnessInput = document.getElementById('brightness-adjust-input');
        this.contrastSlider = document.getElementById('contrast-adjust');
        this.contrastInput = document.getElementById('contrast-adjust-input');
        
        // 预览设置
        this.realtimePreviewCheckbox = document.getElementById('realtime-preview');
        this.previewSizeSelect = document.getElementById('preview-size');
        
        // 操作按钮
        this.resetParamsBtn = document.getElementById('reset-params-btn');
        this.savePresetBtn = document.getElementById('save-preset-btn');
        
        // 设置初始值
        this.setParameterValues();
    }
    
    /**
     * 设置参数控件的初始值
     */
    setParameterValues() {
        this.blendStrengthSlider.value = this.parameters.blendStrength;
        this.blendStrengthInput.value = this.parameters.blendStrength;
        
        this.fusionModeSelect.value = this.parameters.fusionMode;
        
        this.seamlessWeightSlider.value = this.parameters.seamlessWeight;
        this.seamlessWeightInput.value = this.parameters.seamlessWeight;
        
        this.maskOpacitySlider.value = this.parameters.maskOpacity;
        this.maskOpacityInput.value = this.parameters.maskOpacity;
        
        this.brightnessSlider.value = this.parameters.brightnessAdjust;
        this.brightnessInput.value = this.parameters.brightnessAdjust;
        
        this.contrastSlider.value = this.parameters.contrastAdjust;
        this.contrastInput.value = this.parameters.contrastAdjust;
        
        this.realtimePreviewCheckbox.checked = this.parameters.realtimePreview;
        this.previewSizeSelect.value = this.parameters.previewSize;
    }
    
    /**
     * 绑定所有事件监听器
     */
    bindEvents() {
        // 面板展开/折叠
        this.toggleBtn.addEventListener('click', () => this.togglePanel());
        
        // 融合参数事件
        this.bindSliderAndInput('blendStrength', this.blendStrengthSlider, this.blendStrengthInput);
        this.bindSliderAndInput('seamlessWeight', this.seamlessWeightSlider, this.seamlessWeightInput);
        
        this.fusionModeSelect.addEventListener('change', (e) => {
            this.updateParameter('fusionMode', e.target.value);
        });
        
        // 颜色调整事件
        this.bindSliderAndInput('maskOpacity', this.maskOpacitySlider, this.maskOpacityInput);
        this.bindSliderAndInput('brightnessAdjust', this.brightnessSlider, this.brightnessInput);
        this.bindSliderAndInput('contrastAdjust', this.contrastSlider, this.contrastInput);
        
        // 预览设置事件
        this.realtimePreviewCheckbox.addEventListener('change', (e) => {
            this.updateParameter('realtimePreview', e.target.checked);
            if (this.onPreviewToggle) {
                this.onPreviewToggle(e.target.checked);
            }
        });
        
        this.previewSizeSelect.addEventListener('change', (e) => {
            this.updateParameter('previewSize', parseInt(e.target.value));
        });
        
        // 操作按钮事件
        this.resetParamsBtn.addEventListener('click', () => this.resetParameters());
        this.savePresetBtn.addEventListener('click', () => this.savePreset());
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }
    
    /**
     * 绑定滑块和输入框的双向同步
     * @param {string} paramName - 参数名
     * @param {HTMLElement} slider - 滑块元素
     * @param {HTMLElement} input - 输入框元素
     */
    bindSliderAndInput(paramName, slider, input) {
        // 滑块变化时更新输入框和参数
        slider.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            input.value = value;
            this.updateParameter(paramName, value);
        });
        
        // 输入框变化时更新滑块和参数
        input.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            const min = parseFloat(slider.min);
            const max = parseFloat(slider.max);
            
            // 验证范围
            if (value >= min && value <= max) {
                slider.value = value;
                this.updateParameter(paramName, value);
            }
        });
        
        // 输入框失焦时修正值
        input.addEventListener('blur', (e) => {
            const value = parseFloat(e.target.value);
            const min = parseFloat(slider.min);
            const max = parseFloat(slider.max);
            
            const clampedValue = Math.max(min, Math.min(max, value));
            e.target.value = clampedValue;
            slider.value = clampedValue;
            this.updateParameter(paramName, clampedValue);
        });
    }
    
    /**
     * 更新参数值并触发回调
     * @param {string} paramName - 参数名
     * @param {*} value - 参数值
     */
    updateParameter(paramName, value) {
        this.parameters[paramName] = value;
        
        if (this.onParameterChange) {
            this.onParameterChange(paramName, value, this.parameters);
        }
    }
    
    /**
     * 切换面板展开/折叠状态
     */
    togglePanel() {
        this.isCollapsed = !this.isCollapsed;
        
        if (this.isCollapsed) {
            this.panel.classList.add('collapsed');
            this.toggleBtn.textContent = '展开';
        } else {
            this.panel.classList.remove('collapsed');
            this.toggleBtn.textContent = '折叠';
        }
    }
    
    /**
     * 重置所有参数到默认值
     */
    resetParameters() {
        const defaultParams = {
            blendStrength: 0.6,
            fusionMode: 'NORMAL_CLONE',
            seamlessWeight: 0.6,
            maskOpacity: 1.0,
            brightnessAdjust: 0,
            contrastAdjust: 1.0,
            realtimePreview: true,
            previewSize: 200
        };
        
        this.parameters = { ...defaultParams };
        this.setParameterValues();
        
        if (this.onParameterChange) {
            this.onParameterChange('reset', null, this.parameters);
        }
    }
    
    /**
     * 保存当前参数为预设
     */
    savePreset() {
        const presetName = prompt('请输入预设名称:', `预设_${new Date().toLocaleString()}`);
        if (presetName) {
            const presets = this.getPresets();
            presets[presetName] = { ...this.parameters };
            localStorage.setItem('parameterPresets', JSON.stringify(presets));
            alert(`预设 "${presetName}" 已保存！`);
        }
    }
    
    /**
     * 获取已保存的预设
     */
    getPresets() {
        const stored = localStorage.getItem('parameterPresets');
        return stored ? JSON.parse(stored) : {};
    }
    
    /**
     * 加载预设
     * @param {string} presetName - 预设名称
     */
    loadPreset(presetName) {
        const presets = this.getPresets();
        if (presets[presetName]) {
            this.parameters = { ...presets[presetName] };
            this.setParameterValues();
            
            if (this.onParameterChange) {
                this.onParameterChange('loadPreset', presetName, this.parameters);
            }
        }
    }
    
    /**
     * 处理键盘快捷键
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeyboardShortcuts(e) {
        // Ctrl + P: 切换面板
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            this.togglePanel();
        }
        
        // Ctrl + R: 重置参数
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            this.resetParameters();
        }
        
        // Ctrl + S: 保存预设
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            this.savePreset();
        }
    }
    
    /**
     * 获取当前参数
     */
    getParameters() {
        return { ...this.parameters };
    }
    
    /**
     * 设置参数变化回调
     * @param {Function} callback - 回调函数
     */
    setParameterChangeCallback(callback) {
        this.onParameterChange = callback;
    }
    
    /**
     * 设置预览切换回调
     * @param {Function} callback - 回调函数
     */
    setPreviewToggleCallback(callback) {
        this.onPreviewToggle = callback;
    }
    
    /**
     * 显示/隐藏面板
     * @param {boolean} show - 是否显示
     */
    setVisible(show) {
        this.panel.style.display = show ? 'block' : 'none';
    }
    
    /**
     * 启用/禁用所有控件
     * @param {boolean} enabled - 是否启用
     */
    setEnabled(enabled) {
        const inputs = this.panel.querySelectorAll('input, select, button');
        inputs.forEach(input => {
            input.disabled = !enabled;
        });
    }
    
    /**
     * 获取参数的API格式
     */
    getAPIParameters() {
        return {
            blend_strength: this.parameters.blendStrength,
            fusion_mode: this.parameters.fusionMode,
            seamless_weight: this.parameters.seamlessWeight,
            mask_opacity: this.parameters.maskOpacity,
            brightness_adjust: this.parameters.brightnessAdjust,
            contrast_adjust: this.parameters.contrastAdjust
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ParameterPanel;
} else {
    window.ParameterPanel = ParameterPanel;
}

