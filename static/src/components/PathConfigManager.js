/**
 * 路径配置管理器
 * 管理路径选择和配置功能
 */
class PathConfigManager {
    constructor() {
        this.currentPathConfig = {};
        this.currentBrowseCallback = null;
        this.currentBrowsePath = "/";

        // DOM元素
        this.mode1SourcePath = document.getElementById('mode1-source-path');
        this.mode2InputPath = document.getElementById('mode2-input-path');
        this.mode2OutputPath = document.getElementById('mode2-output-path');

        this.browseModes1Source = document.getElementById('browse-mode1-source');
        this.browseMode2Input = document.getElementById('browse-mode2-input');

        // 目录浏览器相关元素
        this.directoryBrowserModal = document.getElementById('directory-browser-modal');
        this.currentPathDisplay = document.getElementById('current-path');
        this.directoryList = document.getElementById('directory-list');
        this.selectDirectoryBtn = document.getElementById('select-directory-btn');
        this.cancelBrowseBtn = document.getElementById('cancel-browse-btn');

        this.bindEvents();
        this.loadCurrentConfig();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 浏览按钮点击事件
        this.browseModes1Source.addEventListener('click', () => {
            this.openDirectoryBrowser((selectedPath) => {
                this.updatePathConfig('mode1_source_path', selectedPath);
            });
        });

        this.browseMode2Input.addEventListener('click', () => {
            this.openDirectoryBrowser((selectedPath) => {
                this.updatePathConfig('mode2_input_path', selectedPath);
            });
        });

        // 目录浏览器事件
        this.selectDirectoryBtn.addEventListener('click', () => {
            this.selectCurrentDirectory();
        });

        this.cancelBrowseBtn.addEventListener('click', () => {
            this.closeDirectoryBrowser();
        });

        // 点击背景关闭模态框
        this.directoryBrowserModal.addEventListener('click', (e) => {
            if (e.target === this.directoryBrowserModal) {
                this.closeDirectoryBrowser();
            }
        });

        // 手动输入路径
        this.mode1SourcePath.addEventListener('change', () => {
            this.updatePathConfig('mode1_source_path', this.mode1SourcePath.value);
        });

        this.mode2InputPath.addEventListener('change', () => {
            this.updatePathConfig('mode2_input_path', this.mode2InputPath.value);
        });
    }

    /**
     * 加载当前路径配置
     */
    async loadCurrentConfig() {
        try {
            const response = await fetch('/api/path_config');
            const config = await response.json();

            this.currentPathConfig = config;
            this.updateUIFromConfig(config);
        } catch (error) {
            console.error('加载路径配置失败:', error);
            alert('加载路径配置失败，请检查网络连接。');
        }
    }

    /**
     * 更新UI显示
     */
    updateUIFromConfig(config) {
        this.mode1SourcePath.value = config.mode1_source_path || '';
        this.mode2InputPath.value = config.mode2_input_path || '';
        this.mode2OutputPath.value = config.mode2_output_path || '';
    }

    /**
     * 更新路径配置
     */
    async updatePathConfig(key, value) {
        try {
            const updateData = {};
            updateData[key] = value;

            const response = await fetch('/api/path_config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(updateData)
            });

            const result = await response.json();

            if (result.status === 'success') {
                this.currentPathConfig = result.config;
                this.updateUIFromConfig(result.config);

                // 触发路径更改事件，让其他组件可以响应
                this.dispatchPathChangeEvent(key, value);
            } else {
                alert(`路径配置更新失败: ${result.message}`);
            }
        } catch (error) {
            console.error('更新路径配置失败:', error);
            alert('更新路径配置失败，请检查网络连接。');
        }
    }

    /**
     * 打开目录浏览器
     */
    openDirectoryBrowser(callback) {
        this.currentBrowseCallback = callback;
        this.currentBrowsePath = this.getCurrentPathGuess();
        this.directoryBrowserModal.classList.remove('hidden');
        this.loadDirectoryContent(this.currentBrowsePath);
    }

    /**
     * 关闭目录浏览器
     */
    closeDirectoryBrowser() {
        this.directoryBrowserModal.classList.add('hidden');
        this.currentBrowseCallback = null;
    }

    /**
     * 选择当前目录
     */
    selectCurrentDirectory() {
        if (this.currentBrowseCallback) {
            this.currentBrowseCallback(this.currentBrowsePath);
        }
        this.closeDirectoryBrowser();
    }

    /**
     * 加载目录内容
     */
    async loadDirectoryContent(path) {
        try {
            const response = await fetch('/api/browse_directory', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ path: path })
            });

            const result = await response.json();

            if (result.status === 'success') {
                this.currentBrowsePath = result.current_path;
                this.currentPathDisplay.textContent = result.current_path;
                this.renderDirectoryList(result.directories);
            } else {
                alert(`浏览目录失败: ${result.message}`);
            }
        } catch (error) {
            console.error('浏览目录失败:', error);
            alert('浏览目录失败，请检查网络连接。');
        }
    }

    /**
     * 渲染目录列表
     */
    renderDirectoryList(directories) {
        this.directoryList.innerHTML = '';

        directories.forEach(dir => {
            const li = document.createElement('li');
            li.textContent = dir.name;
            li.title = dir.path;

            if (dir.name === '..') {
                li.classList.add('parent-dir');
            }

            li.addEventListener('click', () => {
                this.loadDirectoryContent(dir.path);
            });

            this.directoryList.appendChild(li);
        });
    }

    /**
     * 猜测当前应该的路径
     */
    getCurrentPathGuess() {
        // 根据现有配置猜测一个合理的起始路径
        const existingPaths = [
            this.currentPathConfig.mode1_source_path,
            this.currentPathConfig.mode2_input_path,
            this.currentPathConfig.mode2_output_path
        ].filter(p => p && p.length > 0);

        if (existingPaths.length > 0) {
            // 返回第一个现有路径的父目录
            const path = existingPaths[0];
            const lastSlash = path.lastIndexOf('/');
            return lastSlash > 0 ? path.substring(0, lastSlash) : '/';
        }

        // 默认回退到当前网页根目录对应的项目根（相对路径更安全）
        return "/"; // 让用户从根开始浏览
    }

    /**
     * 派发路径更改事件
     */
    dispatchPathChangeEvent(key, value) {
        const event = new CustomEvent('pathConfigChanged', {
            detail: { key, value, config: this.currentPathConfig }
        });
        document.dispatchEvent(event);
    }

    /**
     * 获取当前配置
     */
    getCurrentConfig() {
        return { ...this.currentPathConfig };
    }

    /**
     * 检查路径是否已配置
     */
    isPathConfigured(key) {
        return this.currentPathConfig[key] && this.currentPathConfig[key].length > 0;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PathConfigManager;
} else {
    window.PathConfigManager = PathConfigManager;
}
