/**
 * 实时预览引擎
 * 提供参数调整的即时视觉反馈
 */
class PreviewEngine {
    constructor() {
        this.previewContainer = document.getElementById('preview-container');
        this.previewCanvas = document.getElementById('preview-canvas');
        this.previewCtx = this.previewCanvas.getContext('2d');
        this.applyPreviewBtn = document.getElementById('apply-preview-btn');
        this.resetPreviewBtn = document.getElementById('reset-preview-btn');
        
        // 预览状态
        this.isEnabled = false;
        this.previewSize = 200;
        this.previewRegion = null;
        this.originalImage = null;
        this.maskImage = null;
        this.keypoints = [];
        
        // 防抖计时器
        this.debounceTimer = null;
        this.debounceDelay = 100; // 毫秒
        
        // 事件回调
        this.onApplyToFullImage = null;
        
        this.bindEvents();
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        this.applyPreviewBtn.addEventListener('click', () => this.applyToFullImage());
        this.resetPreviewBtn.addEventListener('click', () => this.resetPreview());
    }
    
    /**
     * 设置预览数据
     * @param {HTMLImageElement} originalImage - 原始图像
     * @param {HTMLImageElement} maskImage - 蒙版图像
     * @param {Array} keypoints - 关键点坐标 [x1, y1, x2, y2]
     */
    setPreviewData(originalImage, maskImage, keypoints) {
        this.originalImage = originalImage;
        this.maskImage = maskImage;
        this.keypoints = keypoints;
        
        if (keypoints && keypoints.length >= 4) {
            this.calculatePreviewRegion();
            this.updatePreview();
        }
    }
    
    /**
     * 计算预览区域
     */
    calculatePreviewRegion() {
        if (!this.keypoints || this.keypoints.length < 4) return;
        
        const [x1, y1, x2, y2] = this.keypoints;
        
        // 计算两点间的中心和距离
        const centerX = (x1 + x2) / 2;
        const centerY = (y1 + y2) / 2;
        const distance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
        
        // 预览区域大小基于关键点距离，但不小于最小尺寸
        const regionSize = Math.max(distance * 1.5, this.previewSize * 0.5);
        
        // 确保预览区域在图像边界内
        const halfSize = regionSize / 2;
        const left = Math.max(0, centerX - halfSize);
        const top = Math.max(0, centerY - halfSize);
        const right = Math.min(this.originalImage.width, centerX + halfSize);
        const bottom = Math.min(this.originalImage.height, centerY + halfSize);
        
        this.previewRegion = {
            x: left,
            y: top,
            width: right - left,
            height: bottom - top,
            centerX: centerX,
            centerY: centerY
        };
    }
    
    /**
     * 更新预览显示
     * @param {Object} parameters - 融合参数
     */
    updatePreview(parameters = null) {
        if (!this.isEnabled || !this.originalImage || !this.maskImage || !this.previewRegion) {
            return;
        }
        
        // 防抖处理
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        
        this.debounceTimer = setTimeout(() => {
            this.renderPreview(parameters);
        }, this.debounceDelay);
    }
    
    /**
     * 渲染预览图像
     * @param {Object} parameters - 融合参数
     */
    renderPreview(parameters) {
        const region = this.previewRegion;
        
        // 调整预览画布大小
        this.previewCanvas.width = this.previewSize;
        this.previewCanvas.height = this.previewSize;
        
        // 清空画布
        this.previewCtx.clearRect(0, 0, this.previewSize, this.previewSize);
        
        try {
            // 绘制原始图像的预览区域
            this.previewCtx.drawImage(
                this.originalImage,
                region.x, region.y, region.width, region.height,
                0, 0, this.previewSize, this.previewSize
            );
            
            // 如果有参数，应用简化的融合效果
            if (parameters && this.maskImage) {
                this.applyPreviewBlending(parameters);
            }
            
        } catch (error) {
            console.warn('预览渲染失败:', error);
            this.showPreviewError();
        }
    }
    
    /**
     * 应用预览融合效果（简化版）
     * @param {Object} parameters - 融合参数
     */
    applyPreviewBlending(parameters) {
        const imageData = this.previewCtx.getImageData(0, 0, this.previewSize, this.previewSize);
        const data = imageData.data;
        
        // 创建临时画布用于蒙版处理
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = this.previewSize;
        tempCanvas.height = this.previewSize;
        const tempCtx = tempCanvas.getContext('2d');
        
        try {
            // 绘制蒙版到临时画布
            const region = this.previewRegion;
            tempCtx.drawImage(
                this.maskImage,
                region.x, region.y, region.width, region.height,
                0, 0, this.previewSize, this.previewSize
            );
            
            const maskImageData = tempCtx.getImageData(0, 0, this.previewSize, this.previewSize);
            const maskData = maskImageData.data;
            
            // 简化的alpha混合
            const opacity = parameters.maskOpacity || 1.0;
            const blendStrength = parameters.blendStrength || 0.6;
            
            for (let i = 0; i < data.length; i += 4) {
                const maskAlpha = maskData[i + 3] / 255.0;
                
                if (maskAlpha > 0.1) {
                    const blendFactor = maskAlpha * opacity * blendStrength;
                    
                    // 简单的颜色混合
                    data[i] = data[i] * (1 - blendFactor) + maskData[i] * blendFactor;     // R
                    data[i + 1] = data[i + 1] * (1 - blendFactor) + maskData[i + 1] * blendFactor; // G
                    data[i + 2] = data[i + 2] * (1 - blendFactor) + maskData[i + 2] * blendFactor; // B
                    
                    // 亮度和对比度调整
                    if (parameters.brightnessAdjust) {
                        data[i] = Math.max(0, Math.min(255, data[i] + parameters.brightnessAdjust));
                        data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + parameters.brightnessAdjust));
                        data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + parameters.brightnessAdjust));
                    }
                    
                    if (parameters.contrastAdjust && parameters.contrastAdjust !== 1.0) {
                        data[i] = Math.max(0, Math.min(255, (data[i] - 128) * parameters.contrastAdjust + 128));
                        data[i + 1] = Math.max(0, Math.min(255, (data[i + 1] - 128) * parameters.contrastAdjust + 128));
                        data[i + 2] = Math.max(0, Math.min(255, (data[i + 2] - 128) * parameters.contrastAdjust + 128));
                    }
                }
            }
            
            this.previewCtx.putImageData(imageData, 0, 0);
            
        } catch (error) {
            console.warn('预览混合失败:', error);
        }
    }
    
    /**
     * 显示预览错误
     */
    showPreviewError() {
        this.previewCtx.fillStyle = '#ff6b6b';
        this.previewCtx.fillRect(0, 0, this.previewSize, this.previewSize);
        
        this.previewCtx.fillStyle = 'white';
        this.previewCtx.font = '16px Arial';
        this.previewCtx.textAlign = 'center';
        this.previewCtx.fillText('预览失败', this.previewSize / 2, this.previewSize / 2);
    }
    
    /**
     * 启用/禁用预览
     * @param {boolean} enabled - 是否启用
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
        
        if (enabled) {
            this.previewContainer.classList.remove('hidden');
            this.updatePreview();
        } else {
            this.previewContainer.classList.add('hidden');
        }
    }
    
    /**
     * 设置预览尺寸
     * @param {number} size - 预览尺寸（像素）
     */
    setPreviewSize(size) {
        this.previewSize = size;
        this.previewCanvas.style.width = size + 'px';
        this.previewCanvas.style.height = size + 'px';
        
        if (this.isEnabled) {
            this.updatePreview();
        }
    }
    
    /**
     * 重置预览
     */
    resetPreview() {
        if (!this.originalImage || !this.previewRegion) return;
        
        // 重新绘制原始图像区域
        this.previewCtx.clearRect(0, 0, this.previewSize, this.previewSize);
        
        const region = this.previewRegion;
        this.previewCtx.drawImage(
            this.originalImage,
            region.x, region.y, region.width, region.height,
            0, 0, this.previewSize, this.previewSize
        );
    }
    
    /**
     * 应用预览效果到全图
     */
    applyToFullImage() {
        if (this.onApplyToFullImage) {
            this.onApplyToFullImage();
        }
    }
    
    /**
     * 设置应用到全图的回调
     * @param {Function} callback - 回调函数
     */
    setApplyToFullImageCallback(callback) {
        this.onApplyToFullImage = callback;
    }
    
    /**
     * 清空预览
     */
    clear() {
        this.originalImage = null;
        this.maskImage = null;
        this.keypoints = [];
        this.previewRegion = null;
        
        this.previewCtx.clearRect(0, 0, this.previewSize, this.previewSize);
        this.setEnabled(false);
    }
    
    /**
     * 获取预览区域信息
     */
    getPreviewRegion() {
        return this.previewRegion;
    }
    
    /**
     * 导出预览图像为DataURL
     */
    exportPreview() {
        return this.previewCanvas.toDataURL('image/png');
    }
    
    /**
     * 设置防抖延迟
     * @param {number} delay - 延迟时间（毫秒）
     */
    setDebounceDelay(delay) {
        this.debounceDelay = delay;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PreviewEngine;
} else {
    window.PreviewEngine = PreviewEngine;
}

