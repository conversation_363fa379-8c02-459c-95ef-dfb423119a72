/**
 * 精确坐标管理器
 * 解决高DPI显示器和复杂变换下的坐标转换精度问题
 */
class CoordinateManager {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.devicePixelRatio = window.devicePixelRatio || 1;
        
        // 变换参数
        this.scale = 1.0;
        this.translateX = 0;
        this.translateY = 0;
        
        // 图像信息
        this.imageWidth = 0;
        this.imageHeight = 0;
        
        // 变换矩阵（使用DOMMatrix确保精度）
        this.transformMatrix = new DOMMatrix();
        
        this.setupCanvas();
    }
    
    /**
     * 设置Canvas的高DPI支持
     */
    setupCanvas() {
        const rect = this.canvas.getBoundingClientRect();
        const cssWidth = rect.width;
        const cssHeight = rect.height;
        
        // 设置实际像素尺寸
        this.canvas.width = cssWidth * this.devicePixelRatio;
        this.canvas.height = cssHeight * this.devicePixelRatio;
        
        // 设置CSS尺寸
        this.canvas.style.width = cssWidth + 'px';
        this.canvas.style.height = cssHeight + 'px';
        
        // 缩放绘图上下文以匹配设备像素比
        this.ctx.scale(this.devicePixelRatio, this.devicePixelRatio);
        
        // 存储CSS尺寸用于坐标转换
        this.canvasWidth = cssWidth;
        this.canvasHeight = cssHeight;
    }
    
    /**
     * 设置图像尺寸
     */
    setImageSize(width, height) {
        this.imageWidth = width;
        this.imageHeight = height;
        this.fitImageToCanvas();
    }
    
    /**
     * 将图像适配到Canvas中心
     */
    fitImageToCanvas() {
        if (this.imageWidth === 0 || this.imageHeight === 0) return;
        
        const scaleX = this.canvasWidth / this.imageWidth;
        const scaleY = this.canvasHeight / this.imageHeight;
        this.scale = Math.min(scaleX, scaleY, 1.0);
        
        // 居中显示
        this.translateX = (this.canvasWidth - this.imageWidth * this.scale) / 2;
        this.translateY = (this.canvasHeight - this.imageHeight * this.scale) / 2;
        
        this.updateTransformMatrix();
    }
    
    /**
     * 更新变换矩阵
     */
    updateTransformMatrix() {
        this.transformMatrix = new DOMMatrix()
            .translate(this.translateX, this.translateY)
            .scale(this.scale, this.scale);
    }
    
    /**
     * 屏幕坐标转图像坐标
     * @param {number} screenX - 屏幕X坐标（相对于Canvas）
     * @param {number} screenY - 屏幕Y坐标（相对于Canvas）
     * @returns {Array} [imageX, imageY] 图像坐标
     */
    screenToImage(screenX, screenY) {
        // 使用逆矩阵进行精确转换
        const inverseMatrix = this.transformMatrix.inverse();
        const point = new DOMPoint(screenX, screenY);
        const transformedPoint = point.matrixTransform(inverseMatrix);
        
        // 限制在图像边界内
        const imageX = Math.max(0, Math.min(this.imageWidth - 1, transformedPoint.x));
        const imageY = Math.max(0, Math.min(this.imageHeight - 1, transformedPoint.y));
        
        return [imageX, imageY];
    }
    
    /**
     * 图像坐标转屏幕坐标
     * @param {number} imageX - 图像X坐标
     * @param {number} imageY - 图像Y坐标
     * @returns {Array} [screenX, screenY] 屏幕坐标
     */
    imageToScreen(imageX, imageY) {
        const point = new DOMPoint(imageX, imageY);
        const transformedPoint = point.matrixTransform(this.transformMatrix);
        return [transformedPoint.x, transformedPoint.y];
    }
    
    /**
     * 应用缩放变换
     * @param {number} factor - 缩放因子
     * @param {number} centerX - 缩放中心X坐标（屏幕坐标）
     * @param {number} centerY - 缩放中心Y坐标（屏幕坐标）
     */
    applyZoom(factor, centerX, centerY) {
        const newScale = this.scale * factor;
        
        // 限制缩放范围
        if (newScale < 0.1 || newScale > 10) return;
        
        // 计算新的平移量，保持缩放中心不变
        this.translateX = centerX - (centerX - this.translateX) * factor;
        this.translateY = centerY - (centerY - this.translateY) * factor;
        this.scale = newScale;
        
        this.updateTransformMatrix();
    }
    
    /**
     * 应用平移变换
     * @param {number} deltaX - X轴平移量
     * @param {number} deltaY - Y轴平移量
     */
    applyPan(deltaX, deltaY) {
        this.translateX += deltaX;
        this.translateY += deltaY;
        this.updateTransformMatrix();
    }
    
    /**
     * 重置变换
     */
    reset() {
        this.scale = 1.0;
        this.translateX = 0;
        this.translateY = 0;
        this.fitImageToCanvas();
    }
    
    /**
     * 获取当前变换信息
     */
    getCurrentTransform() {
        return {
            scale: this.scale,
            translateX: this.translateX,
            translateY: this.translateY,
            matrix: this.transformMatrix
        };
    }
    
    /**
     * 应用变换到Canvas上下文
     */
    applyTransformToContext() {
        this.ctx.save();
        this.ctx.setTransform(
            this.transformMatrix.a * this.devicePixelRatio,
            this.transformMatrix.b * this.devicePixelRatio,
            this.transformMatrix.c * this.devicePixelRatio,
            this.transformMatrix.d * this.devicePixelRatio,
            this.transformMatrix.e * this.devicePixelRatio,
            this.transformMatrix.f * this.devicePixelRatio
        );
    }
    
    /**
     * 恢复Canvas上下文变换
     */
    restoreContext() {
        this.ctx.restore();
    }
    
    /**
     * 获取安全的鼠标坐标（处理offsetX/offsetY的兼容性）
     * @param {MouseEvent} event - 鼠标事件
     * @returns {Array} [x, y] 相对于Canvas的坐标
     */
    getMouseCoordinates(event) {
        let x, y;
        
        if (typeof event.offsetX === 'number' && typeof event.offsetY === 'number') {
            x = event.offsetX;
            y = event.offsetY;
        } else {
            const rect = this.canvas.getBoundingClientRect();
            x = event.clientX - rect.left;
            y = event.clientY - rect.top;
        }
        
        return [x, y];
    }
    
    /**
     * 检查点是否在图像边界内
     * @param {number} imageX - 图像X坐标
     * @param {number} imageY - 图像Y坐标
     * @returns {boolean} 是否在边界内
     */
    isPointInImage(imageX, imageY) {
        return imageX >= 0 && imageX < this.imageWidth && 
               imageY >= 0 && imageY < this.imageHeight;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CoordinateManager;
} else {
    window.CoordinateManager = CoordinateManager;
}

