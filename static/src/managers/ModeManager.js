/**
 * 模式管理器
 * 管理创建蒙版和生成数据两种模式的切换和状态
 */
class ModeManager {
    constructor(coordinateManager, parameterPanel, mainCanvasPreview, pathConfigManager) {
        this.coordinateManager = coordinateManager;
        this.parameterPanel = parameterPanel;
        this.mainCanvasPreview = mainCanvasPreview;
        this.pathConfigManager = pathConfigManager;

        // DOM元素
        this.modeMaskBtn = document.getElementById('mode-mask-btn');
        this.modeGenBtn = document.getElementById('mode-gen-btn');
        this.maskCreationPanel = document.getElementById('mask-creation-panel');
        this.dataGenerationPanel = document.getElementById('data-generation-panel');
        this.instructionText = document.getElementById('instruction-text');

        // 模式状态
        this.currentMode = 'mask-creation';
        this.points = [];
        this.currentImage = null;
        this.currentImagePath = '';

        // 数据生成模式相关
        this.normalSamples = [];
        this.toolMasks = [];
        this.currentSampleIndex = 0;
        this.currentImageIndex = 0;
        this.selectedToolMask = null;
        this.currentSampleDir = null;  // 当前选中的样本目录

        // 蒙版创建状态
        this.polygonComplete = false;  // 多边形是否完成

        // 事件回调
        this.onModeChange = null;
        this.onPointsChange = null;

        this.bindEvents();
        this.initializeMode();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        this.modeMaskBtn.addEventListener('click', () => this.switchMode('mask-creation'));
        this.modeGenBtn.addEventListener('click', () => this.switchMode('data-generation'));
    }

    /**
     * 初始化默认模式
     */
    initializeMode() {
        this.switchMode('mask-creation');
    }

    /**
     * 切换模式
     * @param {string} mode - 模式名称 ('mask-creation' | 'data-generation')
     */
    switchMode(mode) {
        if (this.currentMode === mode) return;

        const previousMode = this.currentMode;
        this.currentMode = mode;

        // 清理状态
        this.clearPoints();
        this.polygonComplete = false;
        this.currentImage = null;
        this.coordinateManager.reset();

        // 更新UI
        this.updateModeUI();

        // 模式特定初始化
        if (mode === 'mask-creation') {
            this.initMaskCreationMode();
        } else if (mode === 'data-generation') {
            this.initDataGenerationMode();
        }

        // 触发模式变化回调
        if (this.onModeChange) {
            this.onModeChange(mode, previousMode);
        }
    }

    /**
     * 更新模式UI
     */
    updateModeUI() {
        if (this.currentMode === 'mask-creation') {
            this.modeMaskBtn.classList.add('active');
            this.modeGenBtn.classList.remove('active');
            this.maskCreationPanel.classList.remove('hidden');
            this.dataGenerationPanel.classList.add('hidden');
            this.parameterPanel.setVisible(false);
            this.mainCanvasPreview.setEnabled(false);
            this.instructionText.textContent = '请在左侧文件浏览器中选择一张图片开始创建蒙版。';
        } else {
            this.modeMaskBtn.classList.remove('active');
            this.modeGenBtn.classList.add('active');
            this.maskCreationPanel.classList.add('hidden');
            this.dataGenerationPanel.classList.remove('hidden');
            this.parameterPanel.setVisible(true);
            this.instructionText.textContent = '请在下方列表中选择一个工具蒙版，然后开始处理样本。';
        }
    }

    /**
     * 初始化蒙版创建模式
     */
    async initMaskCreationMode() {
        try {
            await this.loadSourceImages();
        } catch (error) {
            console.error('加载源图片失败:', error);
            this.instructionText.textContent = '加载源图片失败，请检查网络连接。';
        }
    }

    /**
     * 初始化数据生成模式
     */
    async initDataGenerationMode() {
        try {
            await Promise.all([
                this.loadToolMasks(),
                this.loadNormalSamples()
            ]);

            this.currentSampleIndex = 0;
            this.currentImageIndex = 0;
            this.displayCurrentSampleInfo();

            if (this.normalSamples.length > 0) {
                this.loadNextImageForGeneration();
            }
        } catch (error) {
            console.error('初始化数据生成模式失败:', error);
            this.instructionText.textContent = '初始化失败，请检查数据和网络连接。';
        }
    }

    /**
     * 加载源文件和文件夹列表
     */
    async loadSourceImages(path = '') {
        try {
            const url = `/api/source_images?path=${encodeURIComponent(path)}&t=${new Date().getTime()}`;
            const response = await fetch(url);
            const data = await response.json();

            if (data.error) {
                throw new Error(data.error);
            }

            const sourceImageList = document.getElementById('source-image-list');
            sourceImageList.innerHTML = '';

            // 添加当前路径显示
            this.updateCurrentPathDisplay(data.current_path);

            if (!Array.isArray(data.items) || data.items.length === 0) {
                const li = document.createElement('li');
                li.textContent = '此目录为空';
                li.style.color = '#6c757d';
                li.style.fontStyle = 'italic';
                sourceImageList.appendChild(li);
                return;
            }

            data.items.forEach(item => {
                const li = document.createElement('li');
                li.className = `file-item ${item.type}`;

                if (item.type === 'directory') {
                    // 文件夹
                    li.innerHTML = `
                        <div class="file-item-content">
                            <div class="file-icon">📁</div>
                            <div class="file-info">
                                <div class="file-name">${item.name}</div>
                                <div class="file-type">文件夹</div>
                            </div>
                        </div>
                    `;

                    li.addEventListener('click', () => {
                        console.log('Entering directory:', item.name);
                        this.loadSourceImages(item.path);
                    });

                } else if (item.type === 'image') {
                    // 图片文件
                    const sizeText = this.formatFileSize(item.size);
                    li.innerHTML = `
                        <div class="file-item-content">
                            <div class="file-icon">🖼️</div>
                            <div class="file-info">
                                <div class="file-name">${item.name}</div>
                                <div class="file-type">图片 (${sizeText})</div>
                            </div>
                        </div>
                    `;

                    li.dataset.fullPath = item.full_path;

                    li.addEventListener('click', async () => {
                        console.log('Loading image:', item.name, 'Full path:', item.full_path);

                        // 测试路径是否存在
                        try {
                            const testResponse = await fetch('/api/test_image_path', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ path: item.full_path })
                            });
                            const testResult = await testResponse.json();
                            console.log('[TEST] Path test result:', testResult);
                        } catch (e) {
                            console.error('[TEST] Path test failed:', e);
                        }

                        // 更新UI状态
                        document.querySelectorAll('#source-image-list li.image').forEach(imgItem => {
                            imgItem.classList.remove('selected');
                        });
                        li.classList.add('selected');

                        // 记录当前选择的图片路径，用于创建蒙版时使用
                        this.currentImagePath = item.full_path;

                        // 加载图片到画布
                        this.loadImageToCanvas(item.full_path);
                        this.instructionText.textContent = `正在加载图片: ${item.name}...`;
                    });
                }

                sourceImageList.appendChild(li);
            });
        } catch (error) {
            console.error('加载文件列表失败:', error);
            const sourceImageList = document.getElementById('source-image-list');
            sourceImageList.innerHTML = '';
            const li = document.createElement('li');
            li.textContent = `加载失败: ${error.message}`;
            li.style.color = '#fa383e';
            sourceImageList.appendChild(li);
        }
    }

    /**
     * 更新当前路径显示
     */
    updateCurrentPathDisplay(currentPath) {
        // 找到或创建路径显示元素
        let pathDisplay = document.getElementById('current-source-path');
        if (!pathDisplay) {
            pathDisplay = document.createElement('div');
            pathDisplay.id = 'current-source-path';
            pathDisplay.className = 'current-path-display';

            const sourceList = document.getElementById('source-image-list');
            sourceList.parentNode.insertBefore(pathDisplay, sourceList);
        }

        pathDisplay.textContent = currentPath ? `当前路径: ${currentPath}` : '当前路径: 根目录';
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes < 1024) return bytes + ' B';
        if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
        if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
        return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
    }

    /**
     * 加载工具蒙版
     */
    async loadToolMasks() {
        try {
            const response = await fetch('/api/tool_masks?t=' + new Date().getTime());
            this.toolMasks = await response.json();

            const toolMasksList = document.getElementById('tool-mask-list');
            toolMasksList.innerHTML = '';

            if (!Array.isArray(this.toolMasks) || this.toolMasks.length === 0) {
                const li = document.createElement('li');
                li.textContent = '未找到工具蒙版，请先创建蒙版';
                li.style.color = '#6c757d';
                li.style.fontStyle = 'italic';
                toolMasksList.appendChild(li);
                return;
            }

            this.toolMasks.forEach(mask => {
                const li = document.createElement('li');
                li.textContent = mask.name;
                li.title = mask.full_path || mask.path;
                li.dataset.path = mask.path;

                li.addEventListener('click', () => this.selectToolMask(mask));
                toolMasksList.appendChild(li);
            });
        } catch (error) {
            console.error('加载工具蒙版失败:', error);
            const toolMasksList = document.getElementById('tool-mask-list');
            toolMasksList.innerHTML = '';
            const li = document.createElement('li');
            li.textContent = '加载失败，请检查网络连接';
            li.style.color = '#fa383e';
            toolMasksList.appendChild(li);
        }
    }

    /**
     * 加载正常样本
     */
    async loadNormalSamples() {
        try {
            const response = await fetch('/api/normal_samples?t=' + new Date().getTime());
            this.normalSamples = await response.json();

            const normalSamplesList = document.getElementById('normal-sample-list');
            normalSamplesList.innerHTML = '';

            if (!Array.isArray(this.normalSamples) || this.normalSamples.length === 0) {
                const li = document.createElement('li');
                li.textContent = '未找到样本数据，请检查路径配置';
                li.style.color = '#6c757d';
                li.style.fontStyle = 'italic';
                normalSamplesList.appendChild(li);
                return;
            }

            this.normalSamples.forEach((sample, index) => {
                const li = document.createElement('li');
                li.textContent = sample.sample_name;
                li.title = sample.sample_name;
                li.addEventListener('click', () => this.selectSample(index));
                normalSamplesList.appendChild(li);
            });
        } catch (error) {
            console.error('加载正常样本失败:', error);
            const normalSamplesList = document.getElementById('normal-sample-list');
            normalSamplesList.innerHTML = '';
            const li = document.createElement('li');
            li.textContent = '加载失败，请检查网络连接';
            li.style.color = '#fa383e';
            normalSamplesList.appendChild(li);
        }
    }

    /**
     * 选择工具蒙版
     * @param {Object} mask - 蒙版对象
     */
    selectToolMask(mask) {
        // 更新选中状态
        document.querySelectorAll('#tool-mask-list li').forEach(item => {
            item.classList.remove('selected');
        });

        const selectedLi = document.querySelector(`#tool-mask-list li[data-path="${mask.path}"]`);
        if (selectedLi) {
            selectedLi.classList.add('selected');
        }

        this.selectedToolMask = mask.path;

        // 显示蒙版预览
        const maskPreviewImage = document.getElementById('mask-preview-image');
        const maskPreviewContainer = document.getElementById('mask-preview-container');

        const maskPath = mask.full_path || mask.path;
        if (maskPath.startsWith('/') || maskPath.includes(':\\')) {
            // 绝对路径，编码后传递
            const encodedPath = encodeURIComponent(maskPath);
            maskPreviewImage.src = `/image?p=${encodedPath}&t=${new Date().getTime()}`;
        } else {
            // 相对路径
            maskPreviewImage.src = `/image?p=${encodeURIComponent(maskPath)}&t=${new Date().getTime()}`;
        }
        maskPreviewContainer.style.display = 'block';

        this.instructionText.textContent = `已选择蒙版: ${mask.name}。现在可以开始处理样本。`;
    }

    /**
     * 选择样本
     * @param {number} index - 样本索引
     */
    selectSample(index) {
        this.currentSampleIndex = index;
        this.currentImageIndex = 0;
        this.displayCurrentSampleInfo();
        this.loadNextImageForGeneration();
    }

    /**
     * 加载图片到画布
     * @param {string} imgPath - 图片路径
     */
    loadImageToCanvas(imgPath) {
        this.currentImagePath = imgPath;
        this.clearPoints();

        const img = new Image();

        console.log('[DEBUG] Loading image with path:', imgPath);

        // 构建图片URL - 对于绝对路径，保持完整路径
        let encodedPath;
        if (imgPath.startsWith('/') || (imgPath.length > 1 && imgPath[1] === ':')) {
            // 对于绝对路径，特殊处理以确保开头的/不会丢失
            encodedPath = imgPath.split('/').map(segment => encodeURIComponent(segment)).join('/');
        } else {
            // 相对路径，正常编码
            encodedPath = encodeURIComponent(imgPath);
        }

        // 改为 query 方案，避免 Flask 动态路径捕获时丢失前导斜杠
        const imageUrl = `/image?p=${encodedPath}&t=${new Date().getTime()}`;
        console.log('[DEBUG] Image URL:', imageUrl);

        img.src = imageUrl;

        img.onload = () => {
            console.log('[DEBUG] Image loaded successfully');
            this.currentImage = img;
            this.coordinateManager.setImageSize(img.width, img.height);
            this.redrawCanvas();

            if (this.currentMode === 'mask-creation') {
                this.instructionText.textContent = '图片已加载。请在图片上点击选择多边形顶点。操作提示: Ctrl+滚轮缩放, Ctrl+拖动平移, Ctrl+Z撤销, Enter完成多边形。';
            } else {
                this.instructionText.textContent = '图片已加载。操作提示: Ctrl+滚轮缩放, Ctrl+拖动平移, Ctrl+Z撤销。请点击两个关键点。';
            }
        };

        img.onerror = (error) => {
            console.error('[DEBUG] Image load error:', error);
            console.error('[DEBUG] Failed image path:', imgPath);
            console.error('[DEBUG] Failed image URL:', imageUrl);
            this.instructionText.textContent = `图片加载失败: ${imgPath}，请检查路径配置是否正确。`;
        };
    }

    /**
     * 显示当前样本信息
     */
    displayCurrentSampleInfo() {
        if (this.currentSampleIndex >= this.normalSamples.length) {
            document.getElementById('progress-info').textContent = '所有样本处理完毕！';
            return;
        }

        const sample = this.normalSamples[this.currentSampleIndex];
        const progressInfo = document.getElementById('progress-info');
        progressInfo.textContent = `样本: ${sample.sample_name} (${this.currentSampleIndex + 1}/${this.normalSamples.length}) | 图像: ${this.currentImageIndex + 1}/3`;
    }

    /**
     * 加载下一张图片用于生成
     */
    loadNextImageForGeneration() {
        if (this.currentSampleIndex >= this.normalSamples.length) {
            alert('所有样本已处理完毕！');
            this.switchMode('mask-creation');
            return;
        }

        const sample = this.normalSamples[this.currentSampleIndex];
        if (!sample.images || this.currentImageIndex >= sample.images.length) {
            this.instructionText.textContent = '当前样本没有可用的图片。';
            return;
        }

        const imageInfo = sample.images[this.currentImageIndex];
        const imgPath = imageInfo.full_path || imageInfo.path || imageInfo;
        this.loadImageToCanvas(imgPath);
        this.instructionText.textContent = '请在画布上依次点击两个手腕的位置。';
    }

    /**
     * 添加点
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     */
    addPoint(x, y) {
        if (this.currentMode === 'mask-creation') {
            // 如果多边形已完成，则不再添加点
            if (this.polygonComplete) {
                return;
            }

            this.points.push([x, y]);

            // 更新指导文本
            if (this.points.length < 3) {
                this.instructionText.textContent = `已添加 ${this.points.length} 个点。至少需要3个点，按Enter键完成多边形。`;
            } else {
                this.instructionText.textContent = `已添加 ${this.points.length} 个点。按Enter键完成多边形，Ctrl+Z撤销上一个点。`;
            }
        } else if (this.currentMode === 'data-generation' && this.points.length < 2) {
            this.points.push([x, y]);

            if (this.points.length === 2) {
                this.instructionText.textContent = '两点已选择。点击"处理下一个"按钮生成样本。';

                // 启用预览
                if (this.parameterPanel.getParameters().realtimePreview) {
                    this.updatePreview();
                }
            }
        }

        this.redrawCanvas();

        if (this.onPointsChange) {
            this.onPointsChange(this.points, this.currentMode);
        }
    }

    /**
     * 移除最后一个点
     */
    removeLastPoint() {
        if (this.points.length > 0) {
            this.points.pop();

            // 如果多边形已完成但点数减少，重新设为未完成状态
            if (this.polygonComplete && this.points.length < 3) {
                this.polygonComplete = false;
            }

            // 更新指导文本
            if (this.currentMode === 'mask-creation') {
                if (this.points.length === 0) {
                    this.instructionText.textContent = '请在图片上点击选择多边形顶点。';
                } else if (this.points.length < 3) {
                    this.instructionText.textContent = `已添加 ${this.points.length} 个点。至少需要3个点，按Enter键完成多边形。`;
                } else if (!this.polygonComplete) {
                    this.instructionText.textContent = `已添加 ${this.points.length} 个点。按Enter键完成多边形，Ctrl+Z撤销上一个点。`;
                }
            }

            this.redrawCanvas();

            if (this.onPointsChange) {
                this.onPointsChange(this.points, this.currentMode);
            }
        }
    }

    /**
     * 完成多边形
     */
    completePolygon() {
        if (this.currentMode === 'mask-creation' && this.points.length >= 3 && !this.polygonComplete) {
            this.polygonComplete = true;
            this.instructionText.textContent = `多边形已完成(${this.points.length}个点)。点击"保存蒙版"按钮保存，或按Escape键重新开始。`;
            this.redrawCanvas();
        }
    }

    /**
     * 清空所有点
     */
    clearPoints() {
        this.points = [];
        this.polygonComplete = false;
        this.mainCanvasPreview.clear();

        // 更新指导文本
        if (this.currentMode === 'mask-creation') {
            this.instructionText.textContent = '请在图片上点击选择多边形顶点。';
        }

        if (this.onPointsChange) {
            this.onPointsChange(this.points, this.currentMode);
        }
    }

    /**
     * 更新预览
     */
    updatePreview() {
        if (this.currentMode === 'data-generation' &&
            this.points.length === 2 &&
            this.currentImage &&
            this.selectedToolMask) {

            const keypoints = [
                this.points[0][0], this.points[0][1],
                this.points[1][0], this.points[1][1]
            ];

            this.mainCanvasPreview.setPreviewData(
                this.currentImage,
                this.selectedToolMask,
                keypoints,
                this.currentImagePath
            );
            this.mainCanvasPreview.setEnabled(true);
            this.mainCanvasPreview.updatePreview(this.parameterPanel.getParameters());
        }
    }

    /**
     * 重绘画布
     */
    redrawCanvas() {
        if (!this.currentImage) return;

        // 如果在预览模式且有预览图像，让MainCanvasPreview处理绘制
        if (this.mainCanvasPreview.isInPreviewMode()) {
            this.mainCanvasPreview.redrawCanvas();
            // 仍需要绘制点和线
            this.drawPointsOverlay();
        } else {
            const canvas = document.getElementById('main-canvas');
            const ctx = canvas.getContext('2d');

            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 应用坐标变换并绘制图像
            this.coordinateManager.applyTransformToContext();
            ctx.drawImage(this.currentImage, 0, 0);
            this.coordinateManager.restoreContext();

            // 绘制点和线
            this.drawPointsOverlay();
        }
    }

    /**
     * 绘制点和线的叠加层
     */
    drawPointsOverlay() {
        const canvas = document.getElementById('main-canvas');
        const ctx = canvas.getContext('2d');

        this.coordinateManager.applyTransformToContext();

        if (this.currentMode === 'mask-creation') {
            this.drawPolygon(ctx);
        } else if (this.currentMode === 'data-generation') {
            this.drawKeypoints(ctx);
        }

        this.coordinateManager.restoreContext();
    }

    /**
     * 绘制多边形
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    drawPolygon(ctx) {
        if (this.points.length === 0) return;

        const transform = this.coordinateManager.getCurrentTransform();

        // 根据多边形是否完成选择不同的颜色
        const strokeColor = this.polygonComplete ? '#00ff00' : '#ffaa00';
        const fillColor = this.polygonComplete ? '#00ff00' : '#ffaa00';

        ctx.strokeStyle = strokeColor;
        ctx.lineWidth = 2 / transform.scale;
        ctx.beginPath();

        ctx.moveTo(this.points[0][0], this.points[0][1]);
        for (let i = 1; i < this.points.length; i++) {
            ctx.lineTo(this.points[i][0], this.points[i][1]);
        }

        // 只有在多边形完成时才闭合路径
        if (this.polygonComplete && this.points.length > 2) {
            ctx.closePath();
        }
        ctx.stroke();

        // 如果多边形完成，添加半透明填充
        if (this.polygonComplete && this.points.length > 2) {
            ctx.fillStyle = 'rgba(0, 255, 0, 0.1)';
            ctx.fill();
        }

        // 绘制点
        ctx.fillStyle = fillColor;
        const radius = 4 / transform.scale;

        for (let i = 0; i < this.points.length; i++) {
            const point = this.points[i];
            ctx.beginPath();
            ctx.arc(point[0], point[1], radius, 0, 2 * Math.PI);
            ctx.fill();

            // 为第一个点添加特殊标记
            if (i === 0 && this.points.length > 1) {
                ctx.strokeStyle = '#ff0000';
                ctx.lineWidth = 1 / transform.scale;
                ctx.stroke();
                ctx.strokeStyle = strokeColor;
                ctx.lineWidth = 2 / transform.scale;
            }
        }
    }

    /**
     * 绘制关键点
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     */
    drawKeypoints(ctx) {
        const transform = this.coordinateManager.getCurrentTransform();

        ctx.fillStyle = '#ff0000';
        const radius = 5 / transform.scale;

        for (const point of this.points) {
            ctx.beginPath();
            ctx.arc(point[0], point[1], radius, 0, 2 * Math.PI);
            ctx.fill();
        }
    }

    /**
     * 获取当前模式
     */
    getCurrentMode() {
        return this.currentMode;
    }

    /**
     * 获取当前点集
     */
    getPoints() {
        return [...this.points];
    }

    /**
     * 获取当前图片路径
     */
    getCurrentImagePath() {
        return this.currentImagePath;
    }

    /**
     * 获取选中的工具蒙版
     */
    getSelectedToolMask() {
        return this.selectedToolMask;
    }

    /**
     * 前进到下一个样本/图片
     */
    advanceToNext() {
        if (this.currentMode === 'data-generation') {
            this.currentImageIndex++;
            if (this.currentImageIndex >= 3) {
                this.currentImageIndex = 0;
                this.currentSampleIndex++;
            }

            this.clearPoints();
            this.displayCurrentSampleInfo();
            this.loadNextImageForGeneration();
        }
    }

    /**
     * 设置模式变化回调
     * @param {Function} callback - 回调函数
     */
    setModeChangeCallback(callback) {
        this.onModeChange = callback;
    }

    /**
     * 设置点变化回调
     * @param {Function} callback - 回调函数
     */
    setPointsChangeCallback(callback) {
        this.onPointsChange = callback;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModeManager;
} else {
    window.ModeManager = ModeManager;
}
