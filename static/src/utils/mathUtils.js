/**
 * 数学工具函数
 */
class MathUtils {
    /**
     * 计算两点间的距离
     * @param {Array} point1 - 第一个点 [x, y]
     * @param {Array} point2 - 第二个点 [x, y]
     * @returns {number} 距离
     */
    static distance(point1, point2) {
        const dx = point2[0] - point1[0];
        const dy = point2[1] - point1[1];
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    /**
     * 计算两点间的角度（弧度）
     * @param {Array} point1 - 第一个点 [x, y]
     * @param {Array} point2 - 第二个点 [x, y]
     * @returns {number} 角度（弧度）
     */
    static angle(point1, point2) {
        const dx = point2[0] - point1[0];
        const dy = point2[1] - point1[1];
        return Math.atan2(dy, dx);
    }
    
    /**
     * 计算两点的中点
     * @param {Array} point1 - 第一个点 [x, y]
     * @param {Array} point2 - 第二个点 [x, y]
     * @returns {Array} 中点 [x, y]
     */
    static midpoint(point1, point2) {
        return [
            (point1[0] + point2[0]) / 2,
            (point1[1] + point2[1]) / 2
        ];
    }
    
    /**
     * 将值限制在指定范围内
     * @param {number} value - 值
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 限制后的值
     */
    static clamp(value, min, max) {
        return Math.max(min, Math.min(max, value));
    }
    
    /**
     * 线性插值
     * @param {number} a - 起始值
     * @param {number} b - 结束值
     * @param {number} t - 插值参数 (0-1)
     * @returns {number} 插值结果
     */
    static lerp(a, b, t) {
        return a + (b - a) * t;
    }
    
    /**
     * 将角度从弧度转换为度数
     * @param {number} radians - 弧度值
     * @returns {number} 度数值
     */
    static radiansToDegrees(radians) {
        return radians * (180 / Math.PI);
    }
    
    /**
     * 将角度从度数转换为弧度
     * @param {number} degrees - 度数值
     * @returns {number} 弧度值
     */
    static degreesToRadians(degrees) {
        return degrees * (Math.PI / 180);
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MathUtils;
} else {
    window.MathUtils = MathUtils;
}

