/**
 * 图像处理工具函数
 */
class ImageUtils {
    /**
     * 加载图像
     * @param {string} src - 图像URL
     * @returns {Promise<HTMLImageElement>} 图像元素
     */
    static loadImage(src) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = reject;
            img.src = src;
        });
    }
    
    /**
     * 创建Canvas元素
     * @param {number} width - 宽度
     * @param {number} height - 高度
     * @returns {Object} {canvas, ctx} Canvas元素和上下文
     */
    static createCanvas(width, height) {
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');
        return { canvas, ctx };
    }
    
    /**
     * 调整图像大小
     * @param {HTMLImageElement} image - 源图像
     * @param {number} maxWidth - 最大宽度
     * @param {number} maxHeight - 最大高度
     * @returns {Object} {canvas, ctx} 调整后的Canvas
     */
    static resizeImage(image, maxWidth, maxHeight) {
        const ratio = Math.min(maxWidth / image.width, maxHeight / image.height);
        const width = image.width * ratio;
        const height = image.height * ratio;
        
        const { canvas, ctx } = this.createCanvas(width, height);
        ctx.drawImage(image, 0, 0, width, height);
        
        return { canvas, ctx };
    }
    
    /**
     * 将Canvas转换为Blob
     * @param {HTMLCanvasElement} canvas - Canvas元素
     * @param {string} type - MIME类型
     * @param {number} quality - 质量 (0-1)
     * @returns {Promise<Blob>} Blob对象
     */
    static canvasToBlob(canvas, type = 'image/png', quality = 1.0) {
        return new Promise(resolve => {
            canvas.toBlob(resolve, type, quality);
        });
    }
    
    /**
     * 将Canvas转换为DataURL
     * @param {HTMLCanvasElement} canvas - Canvas元素
     * @param {string} type - MIME类型
     * @param {number} quality - 质量 (0-1)
     * @returns {string} DataURL字符串
     */
    static canvasToDataURL(canvas, type = 'image/png', quality = 1.0) {
        return canvas.toDataURL(type, quality);
    }
    
    /**
     * 从ImageData创建Canvas
     * @param {ImageData} imageData - 图像数据
     * @returns {Object} {canvas, ctx} Canvas元素和上下文
     */
    static imageDataToCanvas(imageData) {
        const { canvas, ctx } = this.createCanvas(imageData.width, imageData.height);
        ctx.putImageData(imageData, 0, 0);
        return { canvas, ctx };
    }
    
    /**
     * 应用图像滤镜
     * @param {ImageData} imageData - 图像数据
     * @param {Object} filters - 滤镜参数
     * @returns {ImageData} 处理后的图像数据
     */
    static applyFilters(imageData, filters = {}) {
        const data = new Uint8ClampedArray(imageData.data);
        const {
            brightness = 0,
            contrast = 1.0,
            saturation = 1.0,
            hue = 0
        } = filters;
        
        for (let i = 0; i < data.length; i += 4) {
            let r = data[i];
            let g = data[i + 1];
            let b = data[i + 2];
            
            // 亮度调整
            if (brightness !== 0) {
                r = Math.max(0, Math.min(255, r + brightness));
                g = Math.max(0, Math.min(255, g + brightness));
                b = Math.max(0, Math.min(255, b + brightness));
            }
            
            // 对比度调整
            if (contrast !== 1.0) {
                r = Math.max(0, Math.min(255, (r - 128) * contrast + 128));
                g = Math.max(0, Math.min(255, (g - 128) * contrast + 128));
                b = Math.max(0, Math.min(255, (b - 128) * contrast + 128));
            }
            
            data[i] = r;
            data[i + 1] = g;
            data[i + 2] = b;
        }
        
        return new ImageData(data, imageData.width, imageData.height);
    }
    
    /**
     * 获取图像的主要颜色
     * @param {HTMLCanvasElement} canvas - Canvas元素
     * @param {number} sampleSize - 采样大小
     * @returns {Array} RGB颜色数组
     */
    static getDominantColor(canvas, sampleSize = 10) {
        const ctx = canvas.getContext('2d');
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        const colorMap = {};
        const step = Math.max(1, Math.floor(data.length / (sampleSize * 4)));
        
        for (let i = 0; i < data.length; i += step * 4) {
            const r = Math.floor(data[i] / 10) * 10;
            const g = Math.floor(data[i + 1] / 10) * 10;
            const b = Math.floor(data[i + 2] / 10) * 10;
            
            const key = `${r},${g},${b}`;
            colorMap[key] = (colorMap[key] || 0) + 1;
        }
        
        let maxCount = 0;
        let dominantColor = [0, 0, 0];
        
        for (const [color, count] of Object.entries(colorMap)) {
            if (count > maxCount) {
                maxCount = count;
                dominantColor = color.split(',').map(Number);
            }
        }
        
        return dominantColor;
    }
    
    /**
     * 计算图像的平均亮度
     * @param {HTMLCanvasElement} canvas - Canvas元素
     * @returns {number} 平均亮度 (0-255)
     */
    static getAverageBrightness(canvas) {
        const ctx = canvas.getContext('2d');
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        let totalBrightness = 0;
        let pixelCount = 0;
        
        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];
            const alpha = data[i + 3];
            
            if (alpha > 0) {
                // 使用感知亮度公式
                const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
                totalBrightness += brightness;
                pixelCount++;
            }
        }
        
        return pixelCount > 0 ? totalBrightness / pixelCount : 0;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ImageUtils;
} else {
    window.ImageUtils = ImageUtils;
}

