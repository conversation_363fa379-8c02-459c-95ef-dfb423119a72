/**
 * 主应用入口文件
 * 集成所有组件，初始化应用
 */
class App {
    constructor() {
        this.canvas = document.getElementById('main-canvas');
        this.ctx = this.canvas.getContext('2d');

        // 初始化组件
        this.coordinateManager = new CoordinateManager(this.canvas);
        this.parameterPanel = new ParameterPanel();
        this.mainCanvasPreview = new MainCanvasPreview(this.canvas, this.coordinateManager);
        this.pathConfigManager = new PathConfigManager();
        this.modeManager = new ModeManager(
            this.coordinateManager,
            this.parameterPanel,
            this.mainCanvasPreview,
            this.pathConfigManager
        );

        // 绑定事件
        this.bindEvents();

        // 设置回调
        this.setupCallbacks();

        // 监听路径配置变化
        this.setupPathConfigListeners();

        console.log('交互式作弊样本生成工具 v2.0 已初始化');
    }

    /**
     * 绑定所有事件监听器
     */
    bindEvents() {
        // Canvas点击事件
        this.canvas.addEventListener('click', (e) => this.handleCanvasClick(e));

        // Canvas滚轮缩放事件
        this.canvas.addEventListener('wheel', (e) => this.handleCanvasWheel(e));

        // Canvas拖拽事件
        this.canvas.addEventListener('mousedown', (e) => this.handleCanvasMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleCanvasMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleCanvasMouseUp(e));
        this.canvas.addEventListener('mouseleave', (e) => this.handleCanvasMouseLeave(e));

        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));

        // 保存蒙版按钮
        const saveMaskBtn = document.getElementById('save-mask-btn');
        if (saveMaskBtn) {
            saveMaskBtn.addEventListener('click', () => this.handleSaveMask());
        }

        // 处理下一个按钮
        const nextSampleBtn = document.getElementById('next-sample-btn');
        if (nextSampleBtn) {
            nextSampleBtn.addEventListener('click', () => this.handleNextSample());
        }

        // 预览控制按钮
        const togglePreviewBtn = document.getElementById('toggle-preview-btn');
        const resetPreviewBtn = document.getElementById('reset-preview-btn');

        if (togglePreviewBtn) {
            togglePreviewBtn.addEventListener('click', () => this.handleTogglePreview());
        }

        if (resetPreviewBtn) {
            resetPreviewBtn.addEventListener('click', () => this.handleResetPreview());
        }

        // 模态对话框按钮
        const confirmBtn = document.getElementById('confirm-btn');
        const rejectBtn = document.getElementById('reject-btn');
        const modalOverlay = document.getElementById('modal-overlay');
        const resultImage = document.getElementById('result-image');

        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => this.handleConfirmResult());
        }

        if (rejectBtn) {
            rejectBtn.addEventListener('click', () => this.handleRejectResult());
        }

        if (modalOverlay) {
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    this.handleRejectResult();
                }
            });
        }

        if (resultImage) {
            resultImage.addEventListener('click', () => {
                if (resultImage.src) {
                    window.open(resultImage.src, '_blank');
                }
            });
        }
    }

    /**
     * 设置路径配置监听器
     */
    setupPathConfigListeners() {
        document.addEventListener('pathConfigChanged', (e) => {
            const { key, value, config } = e.detail;
            console.log(`路径配置已更新: ${key} = ${value}`);

            // 根据配置变化重新加载相关数据
            if (key === 'mode1_source_path' && this.modeManager.getCurrentMode() === 'mask-creation') {
                this.modeManager.loadSourceImages();
            } else if (key === 'mode2_input_path' && this.modeManager.getCurrentMode() === 'data-generation') {
                this.modeManager.loadNormalSamples();
            }
        });
    }

    /**
     * 设置组件间的回调
     */
    setupCallbacks() {
        // 参数面板回调
        this.parameterPanel.setParameterChangeCallback((paramName, value, allParams) => {
            this.handleParameterChange(paramName, value, allParams);
        });

        this.parameterPanel.setPreviewToggleCallback((enabled) => {
            this.previewEngine.setEnabled(enabled);
            if (enabled && this.modeManager.getCurrentMode() === 'data-generation') {
                this.modeManager.updatePreview();
            }
        });

        // 主画布预览回调
        this.mainCanvasPreview.setPreviewUpdateCallback((previewImage) => {
            this.handlePreviewUpdate(previewImage);
        });

        this.mainCanvasPreview.setPreviewErrorCallback((error) => {
            this.handlePreviewError(error);
        });

        // 模式管理器回调
        this.modeManager.setModeChangeCallback((newMode, oldMode) => {
            this.handleModeChange(newMode, oldMode);
        });

        this.modeManager.setPointsChangeCallback((points, mode) => {
            this.handlePointsChange(points, mode);
        });
    }

    /**
     * 处理Canvas点击
     */
    handleCanvasClick(e) {
        if (!this.modeManager.currentImage) return;

        e.preventDefault();

        const [x, y] = this.coordinateManager.getMouseCoordinates(e);
        const [imageX, imageY] = this.coordinateManager.screenToImage(x, y);

        // 检查点是否在图像内
        if (this.coordinateManager.isPointInImage(imageX, imageY)) {
            this.modeManager.addPoint(imageX, imageY);
        }
    }

    /**
     * 处理Canvas滚轮缩放
     */
    handleCanvasWheel(e) {
        if (!this.modeManager.currentImage || !e.ctrlKey) return;

        e.preventDefault();

        const [mouseX, mouseY] = this.coordinateManager.getMouseCoordinates(e);
        const zoomFactor = e.deltaY < 0 ? 1.1 : 0.9;

        this.coordinateManager.applyZoom(zoomFactor, mouseX, mouseY);
        this.modeManager.redrawCanvas();
    }

    /**
     * 处理Canvas鼠标按下
     */
    handleCanvasMouseDown(e) {
        if (!this.modeManager.currentImage || !e.ctrlKey) return;

        this.isDragging = true;
        this.lastDragX = e.clientX;
        this.lastDragY = e.clientY;
        this.canvas.style.cursor = 'grabbing';
    }

    /**
     * 处理Canvas鼠标移动
     */
    handleCanvasMouseMove(e) {
        if (!this.isDragging || !this.modeManager.currentImage) return;

        const deltaX = e.clientX - this.lastDragX;
        const deltaY = e.clientY - this.lastDragY;

        this.coordinateManager.applyPan(deltaX, deltaY);
        this.modeManager.redrawCanvas();

        this.lastDragX = e.clientX;
        this.lastDragY = e.clientY;
    }

    /**
     * 处理Canvas鼠标抬起
     */
    handleCanvasMouseUp(e) {
        this.isDragging = false;
        this.canvas.style.cursor = 'default';
    }

    /**
     * 处理Canvas鼠标离开
     */
    handleCanvasMouseLeave(e) {
        this.isDragging = false;
        this.canvas.style.cursor = 'default';
    }

    /**
     * 处理键盘按键
     */
    handleKeyDown(e) {
        // Ctrl+Z: 撤销最后一个点
        if (e.ctrlKey && e.key === 'z') {
            e.preventDefault();
            this.modeManager.removeLastPoint();
        }

        // Escape: 清空所有点
        if (e.key === 'Escape') {
            e.preventDefault();
            this.modeManager.clearPoints();
        }

        // Enter: 完成多边形(仅在创建蒙版模式下)
        if (e.key === 'Enter') {
            e.preventDefault();
            if (this.modeManager.getCurrentMode() === 'mask-creation') {
                this.modeManager.completePolygon();
            }
        }
    }

    /**
     * 处理保存蒙版
     */
    async handleSaveMask() {
        const points = this.modeManager.getPoints();
        const imagePath = this.modeManager.getCurrentImagePath();

        if (points.length < 3) {
            alert('请至少选择3个点来形成一个多边形。');
            return;
        }

        if (!this.modeManager.polygonComplete) {
            alert('请先按Enter键完成多边形，然后再保存蒙版。');
            return;
        }

        if (!imagePath) {
            alert('请先选择一张图片。');
            return;
        }

        try {
            const response = await fetch('/api/create_mask', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    image_path: imagePath,
                    points: points
                })
            });

            const result = await response.json();

            if (result.status === 'success') {
                alert('蒙版创建成功！');
                this.modeManager.clearPoints();
                // 刷新工具蒙版列表
                if (this.modeManager.loadToolMasks) {
                    await this.modeManager.loadToolMasks();
                }
            } else {
                alert(`错误: ${result.message}`);
            }
        } catch (error) {
            console.error('保存蒙版失败:', error);
            alert('保存蒙版失败，请检查网络连接。');
        }
    }

    /**
     * 处理下一个样本
     */
    async handleNextSample() {
        const points = this.modeManager.getPoints();
        const imagePath = this.modeManager.getCurrentImagePath();
        const toolMask = this.modeManager.getSelectedToolMask();

        if (points.length !== 2) {
            alert('请先在图片上选择两个点。');
            return;
        }

        if (!toolMask) {
            alert('请先从列表中选择一个工具蒙版。');
            return;
        }

        try {
            const parameters = this.parameterPanel.getAPIParameters();

            const response = await fetch('/api/generate_sample', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    image_path: imagePath,
                    points: points,
                    tool_mask: toolMask,
                    parameters: parameters
                })
            });

            const result = await response.json();

            if (result.status === 'success') {
                this.showResultModal(result.generated_image_path);
            } else {
                alert(`生成失败: ${result.message}`);
            }
        } catch (error) {
            console.error('生成样本失败:', error);
            alert('生成样本失败，请检查网络连接。');
        }
    }

    /**
     * 显示结果模态对话框
     */
    showResultModal(imagePath) {
        const modalOverlay = document.getElementById('modal-overlay');
        const resultImage = document.getElementById('result-image');
        const nextSampleBtn = document.getElementById('next-sample-btn');
        const instructionText = document.getElementById('instruction-text');

        if (modalOverlay && resultImage) {
            resultImage.src = `/image/${imagePath}?t=${new Date().getTime()}`;
            modalOverlay.style.display = 'flex';

            if (nextSampleBtn) {
                nextSampleBtn.classList.add('hidden');
            }

            if (instructionText) {
                instructionText.textContent = '请审核生成的结果。点击图片可查看大图。';
            }
        }
    }

    /**
     * 处理确认结果
     */
    handleConfirmResult() {
        const modalOverlay = document.getElementById('modal-overlay');
        const nextSampleBtn = document.getElementById('next-sample-btn');

        if (modalOverlay) {
            modalOverlay.style.display = 'none';
        }

        if (nextSampleBtn) {
            nextSampleBtn.classList.remove('hidden');
        }

        // 前进到下一个样本
        this.modeManager.advanceToNext();
    }

    /**
     * 处理拒绝结果
     */
    handleRejectResult() {
        const modalOverlay = document.getElementById('modal-overlay');
        const nextSampleBtn = document.getElementById('next-sample-btn');
        const instructionText = document.getElementById('instruction-text');

        if (modalOverlay) {
            modalOverlay.style.display = 'none';
        }

        if (nextSampleBtn) {
            nextSampleBtn.classList.remove('hidden');
        }

        // 清空点并重新开始标注
        this.modeManager.clearPoints();

        if (instructionText) {
            instructionText.textContent = '请对当前图片重新进行标注。';
        }
    }

    /**
     * 处理参数变化
     */
    handleParameterChange(paramName, value, allParams) {
        console.log(`参数变化: ${paramName} = ${value}`);

        // 如果实时预览开启且在数据生成模式下，更新预览
        if (allParams.realtimePreview &&
            this.modeManager.getCurrentMode() === 'data-generation' &&
            this.modeManager.getPoints().length === 2) {

            this.mainCanvasPreview.updatePreview(allParams);
        }
    }

    /**
     * 处理预览更新
     */
    handlePreviewUpdate(previewImage) {
        const statusText = document.getElementById('preview-status-text');
        const previewStatus = document.getElementById('preview-status');

        if (statusText && previewStatus) {
            statusText.textContent = '预览已更新';
            previewStatus.className = 'preview-status success';
        }

        // 显示预览控制区域
        const previewControls = document.getElementById('preview-controls');
        if (previewControls) {
            previewControls.classList.remove('hidden');
        }
    }

    /**
     * 处理预览错误
     */
    handlePreviewError(error) {
        const statusText = document.getElementById('preview-status-text');
        const previewStatus = document.getElementById('preview-status');

        if (statusText && previewStatus) {
            statusText.textContent = `预览失败: ${error}`;
            previewStatus.className = 'preview-status error';
        }
    }

    /**
     * 处理切换预览
     */
    handleTogglePreview() {
        this.mainCanvasPreview.togglePreview();

        const toggleBtn = document.getElementById('toggle-preview-btn');
        if (toggleBtn) {
            const isInPreview = this.mainCanvasPreview.isInPreviewMode();
            toggleBtn.textContent = isInPreview ? '显示原图' : '显示预览';
        }
    }

    /**
     * 处理重置预览
     */
    handleResetPreview() {
        this.mainCanvasPreview.hidePreview();
        this.modeManager.redrawCanvas();

        const statusText = document.getElementById('preview-status-text');
        const previewStatus = document.getElementById('preview-status');
        const toggleBtn = document.getElementById('toggle-preview-btn');

        if (statusText && previewStatus) {
            statusText.textContent = '显示原图';
            previewStatus.className = 'preview-status';
        }

        if (toggleBtn) {
            toggleBtn.textContent = '显示预览';
        }
    }

    /**
     * 处理模式变化
     */
    handleModeChange(newMode, oldMode) {
        console.log(`模式切换: ${oldMode} -> ${newMode}`);

        // 根据模式调整UI状态
        if (newMode === 'data-generation') {
            this.mainCanvasPreview.setEnabled(this.parameterPanel.getParameters().realtimePreview);
        } else {
            this.mainCanvasPreview.setEnabled(false);
            // 隐藏预览控制区域
            const previewControls = document.getElementById('preview-controls');
            if (previewControls) {
                previewControls.classList.add('hidden');
            }
        }
    }

    /**
     * 处理点变化
     */
    handlePointsChange(points, mode) {
        // 在数据生成模式下，如果有两个点，更新预览
        if (mode === 'data-generation' &&
            points.length === 2 &&
            this.parameterPanel.getParameters().realtimePreview) {

            this.modeManager.updatePreview();
        }
    }

    /**
     * 获取应用状态
     */
    getAppState() {
        return {
            mode: this.modeManager.getCurrentMode(),
            points: this.modeManager.getPoints(),
            currentImage: this.modeManager.getCurrentImagePath(),
            parameters: this.parameterPanel.getParameters(),
            coordinateTransform: this.coordinateManager.getCurrentTransform()
        };
    }

    /**
     * 重置应用状态
     */
    resetApp() {
        this.modeManager.clearPoints();
        this.coordinateManager.reset();
        this.parameterPanel.resetParameters();
        this.previewEngine.clear();
        console.log('应用状态已重置');
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.app = new App();
        console.log('应用初始化成功');
    } catch (error) {
        console.error('应用初始化失败:', error);
        alert('应用初始化失败，请刷新页面重试。');
    }
});
