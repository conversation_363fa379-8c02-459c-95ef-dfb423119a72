#!/bin/bash
#set -x             # 开启调试模式, 会将sh中信息逐个打印
creator=Moss
Start_Time=$(date +%s)
gpu_count=$(nvidia-smi --query-gpu=gpu_name --format=csv,noheader | wc -l)    # 获取GPU数量
echo "Srart-Train: Your GPU num is ${gpu_count}"


# 获取配置文件路径
#data_pth="/root/share175/sport_datas/run_50M/classify_cheating/train/skl_points"      # 训练集路径
#label_pth="/root/share175/sport_datas/run_50M/classify_cheating/train/datasets/all_label.txt"   # 标签路径
CONFIG="../configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py"
script_pth="/media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2_main0808"
#script_pth="/root/share175/sport_trains/sit_up/classify_cheating/mmaction2-main0626/mmaction2-main"

echo 'Start!'


End_Time_1=$(date +%s)
time1=$(((End_Time_1 - Start_Time) / 60))
echo "ProcessData Use Time $time1 min."


# 使用方法：
  # 设置需要监控的显卡, 间隔WaitTime秒 查看1次显存占用率,
  # 所有监控的显卡都低于占用率阈值时，执行设置好的命令(command); 否则Sleep WaitTime秒继续监测；


# 指定要监测的显卡 GPU ID 列表（用逗号分隔）
#CUDA_DEVICES="0,1,2,3"       # "0,1,2,3"
#CUDA_DEVICES="4,5,6,7"       # "0,1,2,3"
CUDA_DEVICES="0"       # "0,1,2,3"
# 设定显存占用率临界值
THRESHOLD=5       # %
# 设置监测间隔时间(s)
WaitTime=90      # 2分钟查看1次资源占用

# 训练命令
#command="bash train_sport.sh"
command_func() {
    NNODES=${NNODES:-1}
    NODE_RANK=${NODE_RANK:-0}
    PORT=${PORT:-29502}
    MASTER_ADDR=${MASTER_ADDR:-"127.0.0.1"}

    Start_Time2=$(date +%s)

    # Use torchrun instead of torch.distributed.launch
    GPUS=$(echo "$CUDA_DEVICES" | tr ',' '\n' | wc -l)
    if [ "$GPUS" -eq 1 ]; then
        echo "1 GPU And Train use GPU-0"
        python train_m2.py
    elif [ "$GPUS" -gt 1 ]; then
        echo "The Systhem has GPU num is $gpu_count. And Use GPU ${CUDA_DEVICES}"

        # TODO 生成标签和Pkl们
#        python $script_pth/create_vidlabel_lst.py
#        python $script_pth/custom_PoseRGB_extraction.py


        # Set environment variables for torchrun
        export CUDA_VISIBLE_DEVICES=$CUDA_DEVICES
        export MASTER_ADDR=$MASTER_ADDR
        export MASTER_PORT=$PORT

        PYTHONPATH="$(dirname "$0")/..":$PYTHONPATH \
        torchrun \
            --nnodes=$NNODES \
            --nproc_per_node=$GPUS \
            --node_rank=$NODE_RANK \
            $(dirname "$0")/train_m2.py \
            --config $CONFIG \
            --launcher pytorch \
            ${@:3}
    else
        echo "No GPU can be used."
    fi

    End_Time_2=$(date +%s)

    time2=$(((End_Time_2 - Start_Time2) / 60))
    echo "GPU_Monitor: Model Trained Use Time $time2 min."
}




# 定义监测函数，接受多个参数："$CUDA_DEVICES" "$THRESHOLD" "$WaitTime" "$要执行的命令"
monitor_gpu() {
    # 获取传递给函数的 GPU ID 列表参数
    CUDA_DEVICES="$1"
    # 将 GPU ID 列表转换为以空格分隔的格式
    CUDA_DEVICES_LIST=$(echo $CUDA_DEVICES | tr ',' ' ')
    # 定义显存占用率临界值和监测间隔时间
    THRESHOLD="$2"  # %
    WaitTime="$3"

    # 获取传递给函数的要执行的命令
    command="$4"


    while true; do
        # 假设所有显卡都满足条件
        ALL_BELOW_THRESHOLD=1

        for GPU_ID in $CUDA_DEVICES_LIST; do
            # 获取指定 GPU 的显存使用量和总显存大小
            VRAM_INFO=$(nvidia-smi --query-gpu=memory.used,memory.total --format=csv -i $GPU_ID)
            # 读取显存使用量和总显存大小（单位：MiB）
            read -r VRAM_USED_STR VRAM_TOTAL_STR <<< "$(grep -v '^$' <<< "$VRAM_INFO" | tail -n 1 | awk -F ', ' '{print $1,$2}')"

            # 提取数值部分（去掉单位）
            VRAM_USED=$(echo $VRAM_USED_STR | grep -o -E '[0-9]+')
            VRAM_TOTAL=$(echo $VRAM_TOTAL_STR | grep -o -E '[0-9]+')

            # 检查变量是否为空
            if [ -z "$VRAM_USED" ] || [ -z "$VRAM_TOTAL" ]; then
                echo "GPU ${GPU_ID}: Failed to extract VRAM information, setting to 0."
                VRAM_USED=0
                VRAM_TOTAL=1  # 防止除零错误
            fi

            # 避免除零错误
            if [ "$VRAM_TOTAL" -eq 0 ]; then
                echo "GPU ${GPU_ID} Total VRAM is 0, skip calculation."
                VRAM_PERCENTAGE=0
            else
                # 计算显存占用百分比（保留 2 位小数）
                VRAM_PERCENTAGE=$(awk -v used="$VRAM_USED" -v total="$VRAM_TOTAL" 'BEGIN {printf "%.2f", (used / total) * 100}')
            fi

            # 输出当前显存使用情况（便于调试）
            echo "GPU ${GPU_ID} 显存使用情况：${VRAM_USED_STR} / ${VRAM_TOTAL_STR}（Used-Rate：${VRAM_PERCENTAGE}%）"

            # 判断显存占用率是否大于设定的阈值（使用 awk 代替 bc 进行浮点比较）
            if [ $(awk -v perc="$VRAM_PERCENTAGE" -v th="$THRESHOLD" 'BEGIN {print (perc + 0 > th + 0)}') -eq 1 ]; then
                echo "GPU ${GPU_ID} Occupancy > Thres $THRESHOLD%，Continue Wait..."
                # 只要有一张显卡不满足条件，就将标志设置为 0
                ALL_BELOW_THRESHOLD=0
                break
            fi
        done

        # 如果所有显卡都满足条件，则执行传入的命令
        if [ $ALL_BELOW_THRESHOLD -eq 1 ]; then
            echo "All Set GPU Occupancy <= $THRESHOLD%，Start Run My Task as follow..."
            # 使用 eval 执行传入的命令
#            eval "$command"
            command_func
            break
        fi

        # 设置监测间隔（秒）
        sleep ${WaitTime}
        echo "Sleep ${WaitTime} s."
    done
}

# 调用主函数，传入 GPU ID 列表和要执行的命令
monitor_gpu "$CUDA_DEVICES" "$THRESHOLD" "$WaitTime" "command_func"