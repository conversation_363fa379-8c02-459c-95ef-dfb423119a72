#!/usr/bin/env bash
#set -x  # 开启调试模式, 会将sh中信息逐个打印

# 获取GPU数量
gpu_count=$(nvidia-smi --query-gpu=gpu_name --format=csv,noheader | wc -l)
echo "Your GPU num is ${gpu_count}"

# 获取配置文件路径
CONFIG=configs/skeleton/posec3d/slowonly_r50_sport-keypoint.py
CUDA_DEVICES="0"  # 默认使用前4张GPU卡


NNODES=${NNODES:-1}
NODE_RANK=${NODE_RANK:-0}
PORT=${PORT:-29500}
MASTER_ADDR=${MASTER_ADDR:-"127.0.0.1"}
GPUS=$(echo "$CUDA_DEVICES" | tr ',' '\n' | wc -l)

# Set environment variables for torchrun
export CUDA_VISIBLE_DEVICES=$CUDA_DEVICES
export MASTER_ADDR=$MASTER_ADDR
export MASTER_PORT=$PORT


if [ "$gpu_count" -eq 1 ]; then
    echo "1 GPU And Train use GPU-0"
#    python train.py
elif [ "$gpu_count" -gt 1 ]; then
    echo "The Systhem has GPU num is $gpu_count. And Use GPU ${CUDA_DEVICES}"
    # Use torchrun instead of torch.distributed.launch
    PYTHONPATH="$(dirname "$0")/..":$PYTHONPATH \
    torchrun \
        --nnodes=$NNODES \
        --nproc_per_node=$GPUS \
        --node_rank=$NODE_RANK \
        $(dirname "$0")/train.py \
        --config $CONFIG \
        --launcher pytorch \
        ${@:3}

else
    echo "No GPU can be used."
fi
