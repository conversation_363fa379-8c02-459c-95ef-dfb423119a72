#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
方案A：渐进式学习率优化实验训练脚本
基于train_m2.py修改，增加了周期性重启和详细的学习率监控

创建时间：2025-08-12
作者：@moss
"""

import argparse
import os
import os.path as osp
import warnings
from copy import deepcopy

from mmengine.config import Config, DictAction
from mmengine.runner import Runner
from mmengine.logging import MMLogger
from mmengine.hooks import Hook
from mmengine.registry import HOOKS
import torch
import math

from mmaction.registry import RUNNERS
from mmaction.utils import register_all_modules

def parse_args():
    parser = argparse.ArgumentParser(description='Train a recognizer')
    parser.add_argument('config', help='train config file path')
    parser.add_argument('--work-dir', help='the dir to save logs and models')
    parser.add_argument(
        '--resume',
        nargs='?',
        type=str,
        const='auto',
        help='If specify checkpoint path, resume from it, while if not '
        'specify, try to auto resume from the latest checkpoint '
        'in the work directory.')
    parser.add_argument(
        '--amp',
        action='store_true',
        default=False,
        help='enable automatic-mixed-precision training')
    parser.add_argument(
        '--auto-scale-lr',
        action='store_true',
        help='enable automatically scaling LR.')
    parser.add_argument(
        '--cfg-options',
        nargs='+',
        action=DictAction,
        help='override some settings in the used config, the key-value pair '
        'in xxx=yyy format will be merged into config file. If the value to '
        'be overwritten is a list, it should be like key="[a,b]" or key=a,b '
        'It also allows nested list/tuple values, e.g. key="[(a,b),(c,d)]" '
        'Note that the quotation marks are necessary and that no white space '
        'is allowed.')
    parser.add_argument(
        '--launcher',
        choices=['none', 'pytorch', 'slurm', 'mpi'],
        default='none',
        help='job launcher')
    parser.add_argument('--local_rank', type=int, default=0)
    args = parser.parse_args()
    if 'LOCAL_RANK' not in os.environ:
        os.environ['LOCAL_RANK'] = str(args.local_rank)

    return args


@HOOKS.register_module()
class WarmRestartHook(Hook):
    """
    周期性重启钩子 - 实现SGDR (Stochastic Gradient Descent with Warm Restarts)

    Args:
        restart_epochs (list): 重启的epoch列表，如[50, 75]
        restart_lr_ratio (float): 重启时学习率相对于初始学习率的比例
        cosine_annealing (bool): 是否在重启后使用余弦退火
    """

    def __init__(self,
                 restart_epochs=[50],
                 restart_lr_ratio=0.1,
                 cosine_annealing=True,
                 **kwargs):
        super().__init__(**kwargs)
        self.restart_epochs = restart_epochs
        self.restart_lr_ratio = restart_lr_ratio
        self.cosine_annealing = cosine_annealing
        self.initial_lr = None
        self.last_restart_epoch = 0

    def before_train(self, runner):
        """训练开始前记录初始学习率"""
        self.initial_lr = runner.optim_wrapper.get_lr()
        runner.logger.info(f"WarmRestartHook initialized with initial_lr: {self.initial_lr}")
        runner.logger.info(f"Restart epochs: {self.restart_epochs}")

    def before_train_epoch(self, runner):
        """每个epoch开始前检查是否需要重启"""
        current_epoch = runner.epoch

        if current_epoch in self.restart_epochs:
            self._perform_restart(runner, current_epoch)

    def _perform_restart(self, runner, epoch):
        """执行学习率重启"""
        runner.logger.info("=" * 60)
        runner.logger.info(f"🔄 执行周期性重启 - Epoch {epoch}")
        runner.logger.info("=" * 60)

        # 计算重启学习率
        if isinstance(self.initial_lr, (list, tuple)):
            restart_lr = [lr * self.restart_lr_ratio for lr in self.initial_lr]
        else:
            restart_lr = self.initial_lr * self.restart_lr_ratio

        # 设置新的学习率
        runner.optim_wrapper.set_lr(restart_lr)

        runner.logger.info(f"学习率重启: {runner.optim_wrapper.get_lr()}")
        runner.logger.info(f"重启比例: {self.restart_lr_ratio}")

        # 更新最后重启epoch
        self.last_restart_epoch = epoch

        # 如果启用余弦退火，更新调度器
        if self.cosine_annealing:
            self._update_cosine_scheduler(runner, epoch)

    def _update_cosine_scheduler(self, runner, restart_epoch):
        """更新余弦退火调度器"""
        # 计算到下一个重启点或训练结束的epoch数
        next_restart = None
        for ep in self.restart_epochs:
            if ep > restart_epoch:
                next_restart = ep
                break

        if next_restart is None:
            # 如果没有下一个重启点，使用总训练epoch数
            T_max = runner.max_epochs - restart_epoch
        else:
            T_max = next_restart - restart_epoch

        runner.logger.info(f"更新余弦退火: T_max={T_max} epochs")


def main():
    args = parse_args()

    # register all modules in mmaction2 into the registries
    register_all_modules()

    # load config
    cfg = Config.fromfile(args.config)
    cfg.launcher = args.launcher
    if args.cfg_options is not None:
        cfg.merge_from_dict(args.cfg_options)

    # work_dir is determined in this priority: CLI > segment in file > filename
    if args.work_dir is not None:
        # update configs according to CLI args if args.work_dir is not None
        cfg.work_dir = args.work_dir
    elif cfg.get('work_dir', None) is None:
        # use config filename as default work_dir if cfg.work_dir is None
        cfg.work_dir = osp.join('./work_dirs',
                                osp.splitext(osp.basename(args.config))[0])

    # 方案A特殊配置：创建专门的工作目录
    timestamp = torch.cuda.current_device() if torch.cuda.is_available() else 0
    exp_name = f"expA_progressive_lr_{timestamp}"
    cfg.work_dir = osp.join(cfg.work_dir, exp_name)

    # enable automatic-mixed-precision training
    if args.amp is True:
        optim_wrapper = cfg.optim_wrapper.type
        if optim_wrapper == 'AmpOptimWrapper':
            print_log(
                'AMP training is already enabled in your config.',
                logger='current',
                level=logging.WARNING)
        else:
            assert optim_wrapper == 'OptimWrapper', (
                '`--amp` is only supported when the optimizer wrapper type is '
                f'`OptimWrapper` but got {optim_wrapper}.')
            cfg.optim_wrapper.type = 'AmpOptimWrapper'
            cfg.optim_wrapper.loss_scale = 'dynamic'

    # enable automatically scaling LR
    if args.auto_scale_lr:
        if 'auto_scale_lr' in cfg and \
                'enable' in cfg.auto_scale_lr and \
                'base_batch_size' in cfg.auto_scale_lr:
            cfg.auto_scale_lr.enable = True
        else:
            warnings.warn('Can not find "auto_scale_lr" or '
                          '"auto_scale_lr.enable" or '
                          '"auto_scale_lr.base_batch_size" in your configuration file. '
                          'Please update your config according to the comments in '
                          '"configs/recognition/tsn/tsn_r50_1x1x3_100e_kinetics400_rgb.py"')

    # resume is determined in this priority: resume from > auto_resume
    if args.resume == 'auto':
        cfg.resume = True
        cfg.load_from = None
    elif args.resume is not None:
        cfg.resume = True
        cfg.load_from = args.resume

    # 方案A核心功能：添加周期性重启钩子
    if not hasattr(cfg, 'custom_hooks'):
        cfg.custom_hooks = []

    # 添加周期性重启钩子
    cfg.custom_hooks.append(dict(
        type='WarmRestartHook',
        restart_epochs=[50],  # 在第50个epoch重启
        restart_lr_ratio=0.1,  # 重启时学习率为初始学习率的10%
        cosine_annealing=True,  # 重启后使用余弦退火
        priority='HIGH'  # 高优先级，确保在其他钩子之前执行
    ))

    # 添加学习率监控钩子
    @HOOKS.register_module()
    class LRMonitorHook(Hook):
        """学习率监控钩子"""
        def after_train_epoch(self, runner):
            if runner.epoch % 5 == 0 or runner.epoch in [50, 51, 52]:  # 重启前后密集监控
                current_lr = runner.optim_wrapper.get_lr()
                runner.logger.info(f"📊 Epoch {runner.epoch}: LR = {current_lr}")

    cfg.custom_hooks.append(dict(
        type='LRMonitorHook',
        priority='NORMAL'
    ))

    # build the runner from config
    if 'runner_type' not in cfg:
        # build the default runner
        runner = Runner.from_cfg(cfg)
    else:
        # build customized runner from the registry
        # if 'runner_type' is set in the cfg
        runner = RUNNERS.build(cfg)

    # 方案A实验信息记录
    runner.logger.info("=" * 60)
    runner.logger.info("方案A：渐进式学习率优化实验")
    runner.logger.info("=" * 60)
    runner.logger.info("实验配置:")
    runner.logger.info(f"- 3阶段预热: 0.0001 → 0.001 → 0.01")
    runner.logger.info(f"- 分层学习率: cls_head = 1.2x")
    runner.logger.info(f"- 周期性重启: 第50 epoch")
    runner.logger.info(f"- 基于实验14配置: dropout=0.6, weight_decay=0.00015")
    runner.logger.info("=" * 60)

    # start training
    runner.train()


if __name__ == '__main__':
    main()
