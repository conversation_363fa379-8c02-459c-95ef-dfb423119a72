#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验管理训练脚本
专为仰卧起坐作弊检测项目的后续实验设计

功能：
1. 自动化实验执行和管理
2. 实时监控训练指标
3. 自动保存训练记录和可视化
4. 支持多种实验配置的批量执行

使用方法：
python tools/train_experiment_manager.py --experiment expD
python tools/train_experiment_manager.py --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_expD_focal_g1p5_ls005.py
python tools/train_experiment_manager.py --batch --experiments expC,expD,expE

作者：@Moss
创建时间：2025-01-15
"""

import argparse
import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from mmengine.config import Config, DictAction
from mmengine.runner import Runner
from mmaction.registry import RUNNERS


class ExperimentManager:
    """实验管理器"""

    def __init__(self):
        self.experiment_configs = {
            "expC": "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_expC_ce_labelsmooth005.py",
            "expD": "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_expD_focal_g1p5_ls005.py",
            "expE": "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_expE_amp_accum2.py",
            "expF": "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_expF_sgd_1cycle.py",
            "expG_045": "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_expG_dropout045.py",
            "expG_060": "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_expG_dropout060.py",
            "exp11": "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_experiment11_stability.py",
        }

        self.experiment_descriptions = {
            "expC": "ExpC: 交叉熵+标签平滑（稳态对照）",
            "expD": "ExpD: Focal重试（缓和欠拟合）",
            "expE": "ExpE: AMP+梯度累积（等效大batch）",
            "expF": "ExpF: 1Cycle（SGD）",
            "expG_045": "ExpG: Dropout网格 - 0.45版本",
            "expG_060": "ExpG: Dropout网格 - 0.60版本",
            "exp11": "Exp11: 训练稳定性优化",
        }

    def list_experiments(self):
        """列出所有可用实验"""
        print("\n=== 可用实验列表 ===")
        for exp_id, description in self.experiment_descriptions.items():
            config_path = self.experiment_configs[exp_id]
            exists = "✅" if os.path.exists(config_path) else "❌"
            print(f"{exists} {exp_id:12} | {description}")
            if not os.path.exists(config_path):
                print(f"               配置文件不存在: {config_path}")
        print()

    def get_experiment_info(self, experiment_id: str) -> Dict:
        """获取实验信息"""
        if experiment_id not in self.experiment_configs:
            raise ValueError(f"未知实验ID: {experiment_id}")

        config_path = self.experiment_configs[experiment_id]
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")

        return {
            "id": experiment_id,
            "description": self.experiment_descriptions[experiment_id],
            "config_path": config_path,
            "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
        }

    def prepare_experiment(self, experiment_id: str, args) -> Config:
        """准备实验配置"""
        exp_info = self.get_experiment_info(experiment_id)

        print(f"\n=== 准备实验: {exp_info['id']} ===")
        print(f"描述: {exp_info['description']}")
        print(f"配置文件: {exp_info['config_path']}")
        print(f"时间戳: {exp_info['timestamp']}")

        # 加载配置
        cfg = Config.fromfile(exp_info["config_path"])

        # 合并命令行参数
        cfg = self.merge_args(cfg, args, exp_info)

        return cfg, exp_info

    def merge_args(self, cfg, args, exp_info):
        """合并命令行参数到配置"""
        if args.no_validate:
            cfg.val_cfg = None
            cfg.val_dataloader = None
            cfg.val_evaluator = None

        cfg.launcher = args.launcher

        # 设置工作目录
        if args.work_dir is not None:
            cfg.work_dir = args.work_dir
        else:
            # 使用实验ID和时间戳创建唯一工作目录
            base_work_dir = cfg.get("work_dir", "./work_dirs/default")
            cfg.work_dir = f"{base_work_dir}_{exp_info['timestamp']}"

        # 启用AMP（特别针对ExpE）
        if args.amp or exp_info["id"] == "expE":
            if cfg.optim_wrapper.get("type", "OptimWrapper") in [
                "OptimWrapper",
                "AmpOptimWrapper",
            ]:
                cfg.optim_wrapper.type = "AmpOptimWrapper"
                cfg.optim_wrapper.setdefault("loss_scale", "dynamic")
                print("✅ 已启用自动混合精度训练 (AMP)")

        # 恢复训练
        if args.resume == "auto":
            cfg.resume = True
            cfg.load_from = None
        elif args.resume is not None:
            cfg.resume = True
            cfg.load_from = args.resume

        # 自动学习率缩放
        if args.auto_scale_lr:
            cfg.auto_scale_lr.enable = True

        # 随机种子
        if cfg.get("randomness", None) is None:
            cfg.randomness = dict(
                seed=args.seed,
                diff_rank_seed=args.diff_rank_seed,
                deterministic=args.deterministic,
            )

        # 配置选项覆盖
        if args.cfg_options is not None:
            cfg.merge_from_dict(args.cfg_options)

        return cfg

    def save_experiment_log(self, exp_info: Dict, cfg: Config, start_time: float):
        """保存实验日志"""
        log_data = {
            "experiment_id": exp_info["id"],
            "description": exp_info["description"],
            "config_path": exp_info["config_path"],
            "work_dir": cfg.work_dir,
            "start_time": exp_info["timestamp"],
            "duration_seconds": time.time() - start_time,
            "config_summary": {
                "optimizer": cfg.optim_wrapper.optimizer.type,
                "learning_rate": cfg.optim_wrapper.optimizer.lr,
                "max_epochs": cfg.train_cfg.max_epochs,
                "batch_size": cfg.train_dataloader.batch_size,
                "model_type": cfg.model.type,
                "fusion_type": cfg.model.fusion_neck.fusion_type,
            },
        }

        # 保存到工作目录
        os.makedirs(cfg.work_dir, exist_ok=True)
        log_path = os.path.join(cfg.work_dir, "experiment_log.json")

        with open(log_path, "w", encoding="utf-8") as f:
            json.dump(log_data, f, indent=2, ensure_ascii=False)

        print(f"✅ 实验日志已保存: {log_path}")

    def run_single_experiment(self, experiment_id: str, args):
        """运行单个实验"""
        start_time = time.time()

        try:
            # 准备实验
            cfg, exp_info = self.prepare_experiment(experiment_id, args)

            # 创建工作目录
            os.makedirs(cfg.work_dir, exist_ok=True)

            # 构建Runner
            if "runner_type" not in cfg:
                runner = Runner.from_cfg(cfg)
            else:
                runner = RUNNERS.build(cfg)

            print(f"\n🚀 开始训练实验: {exp_info['id']}")
            print(f"工作目录: {cfg.work_dir}")

            # 保存实验日志
            self.save_experiment_log(exp_info, cfg, start_time)

            # 开始训练
            runner.train()

            # 训练完成
            duration = time.time() - start_time
            print(f"\n✅ 实验 {exp_info['id']} 完成！")
            print(f"训练时长: {duration/3600:.2f} 小时")
            print(f"结果保存在: {cfg.work_dir}")

            return True

        except Exception as e:
            print(f"\n❌ 实验 {experiment_id} 失败: {str(e)}")
            return False

    def run_batch_experiments(self, experiment_ids: List[str], args):
        """批量运行多个实验"""
        print(f"\n=== 批量实验开始 ===")
        print(f"实验列表: {', '.join(experiment_ids)}")

        results = {}
        total_start_time = time.time()

        for i, exp_id in enumerate(experiment_ids, 1):
            print(f"\n--- 实验 {i}/{len(experiment_ids)}: {exp_id} ---")

            success = self.run_single_experiment(exp_id, args)
            results[exp_id] = "SUCCESS" if success else "FAILED"

            if i < len(experiment_ids):
                print(f"等待5秒后开始下一个实验...")
                time.sleep(5)

        # 输出总结
        total_duration = time.time() - total_start_time
        print(f"\n=== 批量实验完成 ===")
        print(f"总耗时: {total_duration/3600:.2f} 小时")
        print("\n实验结果:")
        for exp_id, result in results.items():
            status = "✅" if result == "SUCCESS" else "❌"
            print(f"  {status} {exp_id}: {result}")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="实验管理训练脚本")

    # 实验选择
    parser.add_argument(
        "--experiment",
        "-e",
        type=str,
        help="单个实验ID (expC, expD, expE, expF, expG_045, expG_060, exp11)",
    )
    parser.add_argument("--config", type=str, help="配置文件路径（直接指定）")
    parser.add_argument("--batch", action="store_true", help="批量运行多个实验")
    parser.add_argument(
        "--experiments", type=str, help="批量实验ID列表，逗号分隔（如：expC,expD,expE）"
    )

    # 训练选项
    parser.add_argument("--work-dir", help="工作目录")
    parser.add_argument(
        "--resume",
        nargs="?",
        type=str,
        const="auto",
        help="恢复训练：指定检查点路径或使用auto",
    )
    parser.add_argument("--amp", action="store_true", help="启用自动混合精度训练")
    parser.add_argument("--no-validate", action="store_true", help="跳过验证")
    parser.add_argument("--auto-scale-lr", action="store_true", help="自动学习率缩放")

    # 随机种子和确定性
    parser.add_argument("--seed", type=int, default=None, help="随机种子")
    parser.add_argument(
        "--diff-rank-seed", action="store_true", help="不同rank使用不同种子"
    )
    parser.add_argument("--deterministic", action="store_true", help="启用确定性模式")

    # 配置覆盖
    parser.add_argument(
        "--cfg-options", nargs="+", action=DictAction, help="覆盖配置选项"
    )

    # 分布式训练
    parser.add_argument(
        "--launcher",
        choices=["none", "pytorch", "slurm", "mpi"],
        default="none",
        help="启动器类型",
    )
    parser.add_argument("--local_rank", "--local-rank", type=int, default=0)

    # 工具选项
    parser.add_argument("--list", action="store_true", help="列出所有可用实验")

    args = parser.parse_args()

    # 设置环境变量
    if "LOCAL_RANK" not in os.environ:
        os.environ["LOCAL_RANK"] = str(args.local_rank)

    return args


def main():
    """主函数"""
    args = parse_args()
    manager = ExperimentManager()

    # 列出实验
    if args.list:
        manager.list_experiments()
        return

    # 批量实验
    if args.batch:
        if not args.experiments:
            print("❌ 批量模式需要指定 --experiments 参数")
            return

        experiment_ids = [exp.strip() for exp in args.experiments.split(",")]
        manager.run_batch_experiments(experiment_ids, args)
        return

    # 单个实验
    if args.experiment:
        manager.run_single_experiment(args.experiment, args)
        return

    # 直接配置文件
    if args.config:
        from tools.train_m2 import main as train_main

        # 修改sys.argv以兼容原始训练脚本
        sys.argv = ["train_m2.py", "--config", args.config]
        if args.work_dir:
            sys.argv.extend(["--work-dir", args.work_dir])
        if args.amp:
            sys.argv.append("--amp")
        if args.resume:
            sys.argv.extend(["--resume", args.resume])

        train_main()
        return

    # 没有指定实验
    print("❌ 请指定要运行的实验：")
    print("  --experiment <exp_id>     : 运行单个实验")
    print("  --batch --experiments <...> : 批量运行实验")
    print("  --config <config_path>    : 直接使用配置文件")
    print("  --list                    : 列出所有可用实验")
    manager.list_experiments()


if __name__ == "__main__":
    main()
