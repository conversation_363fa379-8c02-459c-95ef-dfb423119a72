# Copyright (c) OpenMMLab. All rights reserved.
"""
基于 train_m2.py 的 Focal Loss 优化训练脚本
Created by @Moss: Focal Loss优化版本 20250807
"""
import argparse
import os
import os.path as osp
from datetime import datetime

from mmengine.config import Config, DictAction
from mmengine.runner import Runner
from mmengine.logging import MMLogger

from mmaction.registry import RUNNERS
from mmaction.utils import register_all_modules


def parse_args():
    parser = argparse.ArgumentParser(description='Train a action recognizer with Focal Loss optimization')
    parser.add_argument('--config', default='configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_focal.py',
                        help='train config file path, 默认使用Focal Loss优化配置')
    parser.add_argument('--work-dir', help='the dir to save logs and models')
    parser.add_argument(
        '--resume',
        nargs='?',
        type=str,
        const='auto',
        help='If specify checkpoint path, resume from it, while if not '
        'specify, try to auto resume from the latest checkpoint '
        'in the work directory.')
    parser.add_argument(
        '--amp',
        action='store_true',
        help='enable automatic-mixed-precision training')
    parser.add_argument(
        '--no-validate',
        action='store_true',
        help='whether not to evaluate the checkpoint during training')
    parser.add_argument(
        '--auto-scale-lr',
        action='store_true',
        help='whether to auto scale the learning rate according to the '
        'actual batch size and the original batch size.')
    parser.add_argument('--seed', type=int, default=None, help='random seed')
    parser.add_argument(
        '--diff-rank-seed',
        action='store_true',
        help='whether or not set different seeds for different ranks')
    parser.add_argument(
        '--deterministic',
        action='store_true',
        help='whether to set deterministic options for CUDNN backend.')
    parser.add_argument(
        '--cfg-options',
        nargs='+',
        action=DictAction,
        help='override some settings in the used config, the key-value pair '
        'in xxx=yyy format will be merged into config file. If the value to '
        'be overwritten is a list, it should be like key="[a,b]" or key=a,b '
        'It also allows nested list/tuple values, e.g. key="[(a,b),(c,d)]" '
        'Note that the quotation marks are necessary and that no white space '
        'is allowed.')
    parser.add_argument(
        '--launcher',
        choices=['none', 'pytorch', 'slurm', 'mpi'],
        default='none',
        help='job launcher')
    parser.add_argument('--local_rank', '--local-rank', type=int, default=0)
    args = parser.parse_args()
    if 'LOCAL_RANK' not in os.environ:
        os.environ['LOCAL_RANK'] = str(args.local_rank)

    return args


def merge_args(cfg, args):
    """Merge CLI arguments to config."""
    if args.no_validate:
        cfg.val_cfg = None
        cfg.val_dataloader = None
        cfg.val_evaluator = None

    cfg.launcher = args.launcher

    # work_dir is determined in this priority: CLI > segment in file > filename
    if args.work_dir is not None:
        # update configs according to CLI args if args.work_dir is not None
        cfg.work_dir = args.work_dir
    elif cfg.get('work_dir', None) is None:
        # use config filename as default work_dir if cfg.work_dir is None
        config_name = osp.splitext(osp.basename(args.config))[0]
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        # 为Focal Loss训练添加特殊标识
        if 'focal' in config_name.lower():
            cfg.work_dir = osp.join('./work_dirs', f'{config_name}_{timestamp}')
        else:
            cfg.work_dir = osp.join('./work_dirs', f'{config_name}_{timestamp}_focal')

    # enable automatic-mixed-precision training
    if args.amp is True:
        optim_wrapper = cfg.optim_wrapper.get('type', 'OptimWrapper')
        assert optim_wrapper in ['OptimWrapper', 'AmpOptimWrapper'], \
            '`--amp` is not supported custom optimizer wrapper type ' \
            f'`{optim_wrapper}.'
        cfg.optim_wrapper.type = 'AmpOptimWrapper'
        cfg.optim_wrapper.setdefault('loss_scale', 'dynamic')

    # resume training
    if args.resume == 'auto':
        cfg.resume = True
        cfg.load_from = None
    elif args.resume is not None:
        cfg.resume = True
        cfg.load_from = args.resume

    # enable auto scale learning rate
    if args.auto_scale_lr:
        cfg.auto_scale_lr.enable = True

    # set random seeds
    if cfg.get('randomness', None) is None:
        cfg.randomness = dict(
            seed=args.seed,
            diff_rank_seed=args.diff_rank_seed,
            deterministic=args.deterministic)

    if args.cfg_options is not None:
        cfg.merge_from_dict(args.cfg_options)

    return cfg


def log_focal_loss_info(logger, cfg):
    """记录Focal Loss相关配置信息"""
    logger.info('='*60)
    logger.info('🚀 开始训练多模态识别模型 - Focal Loss优化版本')
    logger.info(f'📁 配置文件: {cfg.filename if hasattr(cfg, "filename") else "Unknown"}')
    logger.info(f'📂 工作目录: {cfg.work_dir}')
    
    # 记录损失函数信息
    if hasattr(cfg.model, 'cls_head') and hasattr(cfg.model.cls_head, 'loss_cls'):
        loss_info = cfg.model.cls_head.loss_cls
        logger.info(f'🎯 损失函数类型: {loss_info.type}')
        
        if loss_info.type in ['FocalLoss', 'FocalLossWithSmoothing']:
            logger.info(f'   ├─ Alpha权重: {loss_info.get("alpha", "默认")}')
            logger.info(f'   ├─ Gamma参数: {loss_info.get("gamma", 2.0)}')
            if 'label_smoothing' in loss_info:
                logger.info(f'   └─ Label Smoothing: {loss_info.label_smoothing}')
        else:
            logger.info(f'   ⚠️  注意: 当前使用的是 {loss_info.type}，不是Focal Loss')
    
    # 记录自定义Hook信息
    if hasattr(cfg, 'custom_hooks') and cfg.custom_hooks:
        logger.info(f'🔧 自定义Hook数量: {len(cfg.custom_hooks)}')
        for i, hook in enumerate(cfg.custom_hooks):
            hook_type = hook.get('type', 'Unknown')
            if hook_type == 'ClassWeightHook':
                logger.info(f'   ├─ Hook {i+1}: {hook_type} (权重更新间隔: {hook.get("update_interval", 10)}轮)')
            elif hook_type == 'HardSampleHook':
                logger.info(f'   ├─ Hook {i+1}: {hook_type} (困难样本阈值: {hook.get("difficulty_threshold", 0.7)})')
            else:
                logger.info(f'   ├─ Hook {i+1}: {hook_type}')
    else:
        logger.info('⚠️  未检测到自定义Hook，将使用标准训练流程')
    
    # 记录训练参数
    if hasattr(cfg, 'train_cfg'):
        logger.info(f'📊 训练轮数: {cfg.train_cfg.get("max_epochs", "未指定")}')
    
    if hasattr(cfg, 'train_dataloader'):
        logger.info(f'📦 批次大小: {cfg.train_dataloader.get("batch_size", "未指定")}')
    
    logger.info('='*60)


def main():
    args = parse_args()
    
    # 注册所有模块（包括新的Focal Loss和Hook）
    register_all_modules()

    cfg = Config.fromfile(args.config)

    # merge cli arguments to config
    cfg = merge_args(cfg, args)
    
    # 创建工作目录
    os.makedirs(cfg.work_dir, exist_ok=True)
    
    # 初始化日志记录器
    logger = MMLogger.get_instance(name='mmaction', log_file=osp.join(cfg.work_dir, 'train_focal.log'))
    
    # 记录Focal Loss训练配置信息
    log_focal_loss_info(logger, cfg)

    # build the runner from config
    if 'runner_type' not in cfg:
        # build the default runner
        runner = Runner.from_cfg(cfg)
    else:
        # build customized runner from the registry
        # if 'runner_type' is set in the cfg
        runner = RUNNERS.build(cfg)

    # start training
    logger.info('🏃 开始训练...')
    try:
        runner.train()
        logger.info('🎉 训练完成！')
        logger.info(f'💾 模型和日志保存在: {cfg.work_dir}')
    except Exception as e:
        logger.error(f'❌ 训练过程中出现错误: {e}')
        raise


if __name__ == '__main__':
    # Focal Loss优化训练脚本
    main()
