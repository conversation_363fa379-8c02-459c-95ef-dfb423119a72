0: aerobic_push_up
1: aerobic_explosive_push_up
2: aerobic_explosive_support
3: aerobic_leg_circle
4: aerobic_helicopter
5: aerobic_support
6: aerobic_v_support
7: aerobic_horizontal_support
8: aerobic_straight_jump
9: aerobic_illusion
10: aerobic_bent_leg(s)_jump
11: aerobic_pike_jump
12: aerobic_straddle_jump
13: aerobic_split_jump
14: aerobic_scissors_leap
15: aerobic_kick_jump
16: aerobic_off_axis_jump
17: aerobic_butterfly_jump
18: aerobic_split
19: aerobic_turn
20: aerobic_balance_turn
21: volleyball_serve
22: volleyball_block
23: volleyball_first_pass
24: volleyball_defend
25: volleyball_protect
26: volleyball_second_pass
27: volleyball_adjust
28: volleyball_save
29: volleyball_second_attack
30: volleyball_spike
31: volleyball_dink
32: volleyball_no_offensive_attack
33: football_shoot
34: football_long_pass
35: football_short_pass
36: football_through_pass
37: football_cross
38: football_dribble
39: football_trap
40: football_throw
41: football_diving
42: football_tackle
43: football_steal
44: football_clearance
45: football_block
46: football_press
47: football_aerial_duels
48: basketball_pass
49: basketball_drive
50: basketball_dribble
51: basketball_3-point_shot
52: basketball_2-point_shot
53: basketball_free_throw
54: basketball_block
55: basketball_offensive_rebound
56: basketball_defensive_rebound
57: basketball_pass_steal
58: basketball_dribble_steal
59: basketball_interfere_shot
60: basketball_pick-and-roll_defensive
61: basketball_sag
62: basketball_screen
63: basketball_pass-inbound
64: basketball_save
65: basketball_jump_ball
