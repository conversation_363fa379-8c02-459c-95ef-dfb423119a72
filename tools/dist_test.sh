#!/usr/bin/env bash

#set -x
creator=Moss
gpu_count=$(nvidia-smi --query-gpu=gpu_name --format=csv,noheader | wc -l)    # 获取GPU数量
echo "Your GPU num is ${gpu_count}"

# 获取配置文件路径、模型路径、推理设备, 优先使用环境变量，不存在则使用后面的默认值
CONFIG=${CONFIG:-'../configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py'}
CHECKPOINT=${CHECKPOINT:-'work_dirs/pose_rgb_fusion/best_acc_top1_epoch_1.pth'}
CUDA_DEVICES=${CUDA_DEVICES:-"0,1,2,3"}        # 默认使用前4张GPU卡


NNODES=${NNODES:-1}
NODE_RANK=${NODE_RANK:-0}
PORT=${PORT:-29511}

MASTER_ADDR=${MASTER_ADDR:-"127.0.0.1"}
GPUS=$(echo "$CUDA_DEVICES" | tr ',' '\n' | wc -l)

# Set environment variables for torchrun
export CUDA_VISIBLE_DEVICES=$CUDA_DEVICES
export MASTER_ADDR=$MASTER_ADDR


if [ "$GPUS" -eq 1 ]; then
    echo "1 GPU And Train use GPU-0"
    python test.py
elif [ "$GPUS" -gt 1 ]; then
    echo "The Systhem has GPU num is $gpu_count. And Use GPU ${CUDA_DEVICES}"
    PYTHONPATH="$(dirname "$0")/..":$PYTHONPATH \
    torchrun \
        --nnodes=$NNODES \
        --nproc_per_node=$GPUS \
        --node_rank=$NODE_RANK \
        --master_port=$PORT \
        $(dirname "$0")/test.py \
        --config $CONFIG \
        --checkpoint $CHECKPOINT \
        --launcher pytorch \
        ${@:4}
else
    echo "No GPU can be used."
fi



