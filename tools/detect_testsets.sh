#!/usr/bin/env bash
# 仰卧起坐 模型测试脚本    测试集
#set -x             # 开启调试模式, 会将sh中信息逐个打印
creator=Moss
TestMod=True           #
script_pth="/root/share175/sport_trains/sit_up/classify_cheating/mmaction2-main0626/mmaction2-main"


CUDA_DEVICES="4,5,6,7"        # 默认使用前4张GPU卡
#CUDA_DEVICES="0,1,2,3"        # 默认使用前4张GPU卡r


# 数据
#data_pth="/root/share175/sport_test/sit_up/classify_cheat/pose_rgb"    # 测试集
data_pth="/root/share175/sport_test/sit_up/classify_cheat/pose_rgb"    # 测试集



Method="Copy"          # Cut、Copy TODO 很重要 务必检查



# 1 生成默认标签 0_default; 2 生成pkl TODO 测试集模式下 需注释掉 --default_label $default_lable
filename=$(basename "$data_pth")
video_list="$data_pth/../Fusion_datasets"       #
#python $script_pth/create_vidlabel_lst.py --TestMod ${TestMod} --data_pth $data_pth  --dataset_dir $video_list
#python $script_pth/custom_PoseRGB_extraction.py --TestMod ${TestMod} --video-list $video_list

# 3. 执行测试model:
#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250801_100506_R50_512/best_acc_top1_epoch_66.pth"     #  11 R50-512 padding
#CONFIG="$script_pth/configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_ResNetTSM512.py"

#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250730_121806_padding/best_acc_top1_epoch_54.pth"     #  10 padding   Best Model
#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250805_045639/best_acc_top1_epoch_72.pth"     #  12 padding
#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250806_045654/best_acc_top1_epoch_48.pth"     #  13 Base 1.65W
#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250806_072035_atten/best_acc_top1_epoch_79.pth"     #  Base 13 attention fusion

#CONFIG="$video_list/multimodal_poseGCN-rgbR50_fusion.py"

CHECKPOINT=$CHECKPOINT CUDA_DEVICES=$CUDA_DEVICES CONFIG=$CONFIG PORT=29590 \
bash ${script_pth}/tools/dist_test.sh

# 4. Match Or Copy ||  处理后,删除路径中的所有空文件夹
score_thre=0.90         # 仅在Match下产生效果
#python $script_pth/Select_samples.py --datasets $video_list --split $Method --score_thre $score_thre  --empty_pth $data_pth
#

echo $data_pth
echo 'okk.'
