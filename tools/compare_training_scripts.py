#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练脚本对比工具
用于展示 train_m2.py 和 train_focal.py 的区别
"""

import os
import sys
import os.path as osp

# 添加项目根目录到Python路径
sys.path.insert(0, osp.join(osp.dirname(osp.abspath(__file__)), '..'))

def print_comparison():
    """打印两种训练方式的对比"""
    print("=" * 80)
    print("🔍 训练脚本对比分析")
    print("=" * 80)
    
    print("\n📋 脚本概览:")
    print("├─ train_m2.py     : 原始训练脚本，使用标准CrossEntropyLoss")
    print("└─ train_focal.py  : Focal Loss优化训练脚本，支持高级损失函数和Hook")
    
    print("\n🎯 主要区别:")
    
    print("\n1. 默认配置文件:")
    print("   ├─ train_m2.py    : multimodal_poseGCN-rgbR50_fusion.py")
    print("   └─ train_focal.py : multimodal_poseGCN-rgbR50_fusion_focal.py")
    
    print("\n2. 损失函数:")
    print("   ├─ train_m2.py    : CrossEntropyLoss (标准交叉熵)")
    print("   └─ train_focal.py : FocalLossWithSmoothing (Focal Loss + Label Smoothing)")
    
    print("\n3. 训练策略:")
    print("   ├─ train_m2.py    : 标准训练流程")
    print("   └─ train_focal.py : 动态权重调整 + 困难样本挖掘")
    
    print("\n4. 正则化:")
    print("   ├─ train_m2.py    : dropout=0.5")
    print("   └─ train_focal.py : dropout=0.6, batch_size=64 (vs 128)")
    
    print("\n5. 日志记录:")
    print("   ├─ train_m2.py    : 标准日志")
    print("   └─ train_focal.py : 详细的Focal Loss配置信息和Hook状态")
    
    print("\n6. 工作目录:")
    print("   ├─ train_m2.py    : ./work_dirs/{config_name}")
    print("   └─ train_focal.py : ./work_dirs/{config_name}_{timestamp}")


def print_usage_examples():
    """打印使用示例"""
    print("\n" + "=" * 80)
    print("📚 使用示例")
    print("=" * 80)
    
    print("\n🔸 标准训练 (train_m2.py):")
    print("```bash")
    print("# 使用原始配置和CrossEntropyLoss")
    print("python tools/train_m2.py --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py")
    print("```")
    
    print("\n🔸 Focal Loss优化训练 (train_focal.py):")
    print("```bash")
    print("# 使用Focal Loss优化配置")
    print("python tools/train_focal.py")
    print("")
    print("# 或指定配置文件")
    print("python tools/train_focal.py --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_focal.py")
    print("```")
    
    print("\n🔸 混合使用:")
    print("```bash")
    print("# 使用原始配置但通过参数启用Focal Loss")
    print("python tools/train_focal.py --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py \\")
    print("    --cfg-options model.cls_head.loss_cls.type=FocalLossWithSmoothing \\")
    print("                  model.cls_head.loss_cls.alpha=auto \\")
    print("                  model.cls_head.loss_cls.gamma=2.0")
    print("```")


def print_expected_improvements():
    """打印预期改进效果"""
    print("\n" + "=" * 80)
    print("📈 预期改进效果")
    print("=" * 80)
    
    print("\n基于实验分析，Focal Loss优化预期能够:")
    
    print("\n🎯 性能指标改进:")
    print("   ├─ 异常检出率: 96.53% → 97%+")
    print("   ├─ 正常误判率: 1.65% → 1.5%以下")
    print("   └─ 模型稳定性: 减少验证集波动")
    
    print("\n🔧 训练过程改进:")
    print("   ├─ 过拟合缓解: 通过Label Smoothing和增强正则化")
    print("   ├─ 困难样本处理: 自动识别和重点训练")
    print("   ├─ 动态调整: 基于验证集表现自动优化权重")
    print("   └─ 类别平衡: 动态处理类别不平衡问题")
    
    print("\n📊 监控指标:")
    print("   ├─ class_weight_*: 各类别权重变化")
    print("   ├─ hard_sample_ratio: 困难样本比例")
    print("   ├─ difficulty_threshold: 困难样本阈值")
    print("   └─ abnormal_detection_rate: 异常检出率")


def check_files_exist():
    """检查相关文件是否存在"""
    print("\n" + "=" * 80)
    print("🔍 文件检查")
    print("=" * 80)
    
    files_to_check = [
        'tools/train_m2.py',
        'tools/train_focal.py',
        'configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py',
        'configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_focal.py',
        'mmaction/models/losses/focal_loss.py',
        'mmaction/engine/hooks/class_weight_hook.py',
        'mmaction/engine/hooks/hard_sample_hook.py'
    ]
    
    print("\n检查关键文件:")
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} (缺失)")
    
    print("\n如果有文件缺失，请检查实施是否完整。")


def main():
    """主函数"""
    print_comparison()
    print_usage_examples()
    print_expected_improvements()
    check_files_exist()
    
    print("\n" + "=" * 80)
    print("🚀 建议使用流程")
    print("=" * 80)
    
    print("\n1. 首次使用: 建议使用 train_focal.py 进行Focal Loss优化训练")
    print("2. 对比实验: 可以同时运行两种脚本，对比效果差异")
    print("3. 参数调优: 通过 --cfg-options 微调Focal Loss参数")
    print("4. 监控训练: 关注日志中的Hook状态和权重变化")
    
    print("\n💡 提示: train_focal.py 完全兼容 train_m2.py 的所有参数!")
    print("=" * 80)


if __name__ == '__main__':
    main()
