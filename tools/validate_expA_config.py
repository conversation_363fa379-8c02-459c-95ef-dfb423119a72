#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
方案A配置文件验证脚本
验证配置文件的正确性和关键参数设置

创建时间：2025-08-12
"""

import sys
import os

def validate_config():
    """验证方案A配置文件"""

    print("=" * 60)
    print("方案A配置文件验证")
    print("=" * 60)

    # 加载配置文件（简化版本，直接执行Python文件）
    config_path = 'configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_expA_progressive_lr.py'

    try:
        # 简单的语法检查
        with open(config_path, 'r', encoding='utf-8') as f:
            config_content = f.read()

        # 执行配置文件获取配置
        config_globals = {}
        exec(config_content, config_globals)

        print(f"✅ 配置文件语法检查通过: {config_path}")

        # 提取关键配置
        model = config_globals.get('model', {})
        optim_wrapper = config_globals.get('optim_wrapper', {})
        param_scheduler = config_globals.get('param_scheduler', [])

    except Exception as e:
        print(f"❌ 配置文件验证失败: {e}")
        return False

    # 验证关键配置
    success = True

    # 1. 验证模型配置
    print("\n📋 模型配置验证:")
    if model.get('type') == 'MultiModalRecognizer':
        print("✅ 模型类型: MultiModalRecognizer")
    else:
        print(f"❌ 模型类型错误: {model.get('type')}")
        success = False

    # 2. 验证融合模块配置
    fusion_neck = model.get('fusion_neck', {})
    if fusion_neck.get('fusion_type') == 'attention':
        print("✅ 融合类型: attention")
    else:
        print(f"❌ 融合类型错误: {fusion_neck.get('fusion_type')}")
        success = False

    if fusion_neck.get('dropout') == 0.6:
        print("✅ Dropout: 0.6 (实验14最佳配置)")
    else:
        print(f"⚠️  Dropout: {fusion_neck.get('dropout')} (期望: 0.6)")

    # 3. 验证优化器配置
    print("\n🔧 优化器配置验证:")
    optimizer = optim_wrapper.get('optimizer', {})
    if optimizer.get('type') == 'AdamW':
        print("✅ 优化器: AdamW")
    else:
        print(f"❌ 优化器错误: {optimizer.get('type')}")
        success = False

    if optimizer.get('weight_decay') == 0.00015:
        print("✅ 权重衰减: 0.00015 (实验14最佳配置)")
    else:
        print(f"⚠️  权重衰减: {optimizer.get('weight_decay')} (期望: 0.00015)")

    # 4. 验证分层学习率
    print("\n📈 分层学习率验证:")
    paramwise_cfg = optim_wrapper.get('paramwise_cfg', {})
    custom_keys = paramwise_cfg.get('custom_keys', {})

    expected_lr_mult = {
        'image_backbone': 0.1,
        'pose_backbone': 1.0,
        'fusion_neck': 1.0,
        'cls_head': 1.2  # 方案A的关键改进
    }

    for key, expected_mult in expected_lr_mult.items():
        if key in custom_keys and custom_keys[key].get('lr_mult') == expected_mult:
            if key == 'cls_head':
                print(f"✅ {key}: {custom_keys[key]['lr_mult']}x (方案A改进)")
            else:
                print(f"✅ {key}: {custom_keys[key]['lr_mult']}x")
        else:
            actual_mult = custom_keys.get(key, {}).get('lr_mult', 'N/A')
            print(f"❌ {key}: {actual_mult} (期望: {expected_mult})")
            success = False

    # 5. 验证学习率调度
    print("\n⏰ 学习率调度验证:")
    schedulers = param_scheduler
    
    if len(schedulers) >= 3:
        print(f"✅ 调度器数量: {len(schedulers)} (3阶段预热)")
        
        # 检查第一阶段
        stage1 = schedulers[0]
        if (stage1.get('type') == 'LinearLR' and
            stage1.get('start_factor') == 0.0001 and
            stage1.get('begin') == 0 and stage1.get('end') == 3):
            print("✅ 阶段1: 极慢预热 (0-3 epochs, factor=0.0001)")
        else:
            print("❌ 阶段1配置错误")
            success = False

        # 检查第二阶段
        stage2 = schedulers[1]
        if (stage2.get('type') == 'LinearLR' and
            stage2.get('start_factor') == 0.001 and
            stage2.get('begin') == 3 and stage2.get('end') == 8):
            print("✅ 阶段2: 加速预热 (3-8 epochs, factor=0.001)")
        else:
            print("❌ 阶段2配置错误")
            success = False

        # 检查第三阶段
        stage3 = schedulers[2]
        if (stage3.get('type') == 'CosineAnnealingLR' and
            stage3.get('begin') == 8 and stage3.get('end') == 100):
            print("✅ 阶段3: 余弦退火 (8-100 epochs)")
        else:
            print("❌ 阶段3配置错误")
            success = False
    else:
        print(f"❌ 调度器数量不足: {len(schedulers)} (期望: ≥3)")
        success = False

    # 6. 验证梯度裁剪
    print("\n✂️  梯度裁剪验证:")
    clip_grad = optim_wrapper.get('clip_grad', {})
    if clip_grad.get('max_norm') == 10:
        print("✅ 梯度裁剪: max_norm=10 (实验14最佳配置)")
    else:
        print(f"⚠️  梯度裁剪: max_norm={clip_grad.get('max_norm')} (期望: 10)")

    # 7. 验证训练配置
    print("\n🎯 训练配置验证:")
    train_cfg = config_globals.get('train_cfg', {})
    if train_cfg.get('max_epochs') == 100:
        print("✅ 训练轮数: 100 epochs")
    else:
        print(f"⚠️  训练轮数: {train_cfg.get('max_epochs')} (期望: 100)")
    
    # 总结
    print("\n" + "=" * 60)
    if success:
        print("🎉 配置验证通过！方案A配置正确")
        print("\n核心改进点:")
        print("- ✅ 3阶段预热策略")
        print("- ✅ cls_head学习率1.2倍")
        print("- ✅ 基于实验14最佳配置")
        print("- ✅ 保持Baseline兼容性")
    else:
        print("❌ 配置验证失败！请检查配置文件")
    
    print("=" * 60)
    return success

if __name__ == '__main__':
    validate_config()
