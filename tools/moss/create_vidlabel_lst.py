# -*-coding:utf-8-*-
"""
50m项目：创建 vid跑道标签 载入样本pkl
"""
import argparse
from pathlib import Path



def read_labels(label_pth):
    """
    获取标签信息，与文件夹关联
    """
    with open(label_pth, 'r') as flab:
        labels = [line.strip() for line in flab.readlines() if len(line) > 1]

    return labels







if __name__ == '__main__':
    parser = argparse.ArgumentParser(prog='训练-验证标签生成')
    parser.add_argument('--data_pth', type=str,
                        default="/media/pyl/WD_Blue_1T/dockerSpace/TimeConv_classify/mmaction2-main/demo/demo_moss", help='训练集标签来源')

    parser.add_argument('--label_name', default="labels.txt", help='标签名在数据路径中，并与文件夹对应')
    opt = parser.parse_args()

    label_pth = Path(opt.data_pth) / opt.label_name
    labels = read_labels(label_pth)
    print(labels)


