# -*-coding:utf-8-*-
"""
可视化数据预处理 代码
TODO 集成到数据预处理的接口
"""
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import ConnectionPatch


def plot_visable(akeypoints=None):
    # 假设 keypoints 是你的关键点集，形状为 (1, 48, 17, 2)
    # 这里生成随机数据作为示例
    if akeypoints is None:
        akeypoints = np.random.randint(0, 580, size=(1, 48, 17, 2))

    # 创建黑色背景图
    fig, ax = plt.subplots(figsize=(6, 6))
    ax.set_xlim(0, 580)
    ax.set_ylim(0, 580)
    ax.set_facecolor('black')  # 设置背景颜色为黑色
    ax.axis('off')  # 关闭坐标轴

    # 绘制一个黑色的背景矩形，确保背景始终在最底层
    ax.add_patch(plt.Rectangle((0, 0), 580, 580, facecolor='black'))

    # COCO 数据集的关键点连接顺序
    skeleton = [
        [15, 13], [13, 11], [16, 14], [14, 12], [11, 12], [5, 11], [6, 12], [5, 6], [5, 7], [6, 8],
        [7, 9], [8, 10], [1, 2], [0, 1], [0, 2], [1, 3], [2, 4], [3, 5], [4, 6]
    ]

    # 遍历每一帧的关键点
    for frame in range(akeypoints.shape[1]):
        frame_keypoints = akeypoints[0, frame]  # 当前帧的关键点

        # 绘制关键点
        ax.scatter(frame_keypoints[:, 0], frame_keypoints[:, 1], color='white', s=10)

        # 绘制关键点之间的连接线
        for conn in skeleton:
            start_point = frame_keypoints[conn[0]]
            end_point = frame_keypoints[conn[1]]
            ax.add_patch(ConnectionPatch(start_point, end_point, "data", "data", color='white', lw=1))

    # 保存图像
    plt.savefig("/dockerSpace/train/datasets/output1.png", facecolor='black', bbox_inches='tight', pad_inches=0)

    # 显示图像
    # plt.show()

    return