#!/usr/bin/env bash

set -x
CONFIG="../configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py"
CUDA_DEVICES='0'
GPUS=$(echo "$CUDA_DEVICES" | tr ',' '\n' | wc -l)

NNODES=${NNODES:-1}
NODE_RANK=${NODE_RANK:-0}
PORT=${PORT:-29502}
MASTER_ADDR=${MASTER_ADDR:-"127.0.0.1"}


export CUDA_VISIBLE_DEVICES=$CUDA_DEVICES
export MASTER_ADDR=$MASTER_ADDR
export MASTER_PORT=$PORT

PYTHONPATH="$(dirname "$0")/..":$PYTHONPATH \
torchrun \
    --nnodes=$NNODES \
    --nproc_per_node=1 \
    --node_rank=$NODE_RANK \
    $(dirname "$0")/train_m2.py \
    --config $CONFIG \
    --launcher pytorch \
    ${@:3}



#CONFIG=$1
#GPUS=$2
#NNODES=${NNODES:-1}
#NODE_RANK=${NODE_RANK:-0}
#PORT=${PORT:-29500}
#MASTER_ADDR=${MASTER_ADDR:-"127.0.0.1"}
#
#PYTHONPATH="$(dirname $0)/..":$PYTHONPATH \
#python -m torch.distributed.launch --nnodes=$NNODES --node_rank=$NODE_RANK --master_addr=$MASTER_ADDR \
#    --nproc_per_node=$GPUS --master_port=$PORT $(dirname "$0")/train.py $CONFIG --launcher pytorch ${@:3}
# Any arguments from the third one are captured by ${@:3}
