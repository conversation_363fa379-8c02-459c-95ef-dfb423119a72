#!/usr/bin/env bash
# 仰卧起坐 模型测试脚本
#set -x             # 开启调试模式, 会将sh中信息逐个打印
creator=Moss
TestMod=True           #
script_pth="/root/share175/sport_trains/sit_up/classify_cheating/mmaction2-main0626/mmaction2-main"


CUDA_DEVICES="4,5,6,7"        # 默认使用前4张GPU卡
#CUDA_DEVICES="0,1,2,3"        # 默认使用前4张GPU卡r


# 数据
#data_pth="/root/share175/sport_test/sit_up/classify_cheat/pose_rgb"    # 测试集
#data_pth="/root/share175/sport_test/sit_up/classify_cheat/pose_rgb"    # 测试集
data_pth="/root/share175/sport_datas/sit_up/classify_cheating/train/pose_rgb/1_fake/fake_tool/yskj_26floor"    # samples
default_lable='1_default'         # 默认标签为0, 测试集模式下，默认为None

Method="Copy"          # Cut、Copy、Match TODO 很重要 务必检查
score_thre=0.90         # 仅在Match下产生效果


# 1 生成默认标签 0_default; 2 生成pkl
filename=$(basename "$data_pth")
video_list="$data_pth/../Fusion_datasets_$filename"       #
python $script_pth/create_vidlabel_lst.py --TestMod ${TestMod} --data_pth $data_pth  --dataset_dir $video_list --default_label $default_lable
python $script_pth/custom_PoseRGB_extraction.py --TestMod ${TestMod} --video-list $video_list

# 3. 执行测试
#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250712_082151/best_acc_top1_epoch_89.pth"     # 1 fake_det best
#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250716_153227/best_acc_top1_epoch_79.pth"     # 2
#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250717_152428/best_acc_top1_epoch_82.pth"     #  3 norm_err best
#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250718_123441/best_acc_top1_epoch_61.pth"     #  4
#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250722_123722/best_acc_top1_epoch_66.pth"     #  5
#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250724_183619/best_acc_top1_epoch_98.pth"     #  6 Bester
#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250726_031854/best_acc_top1_epoch_83.pth"     #  7
#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250728_055150/best_acc_top1_epoch_83.pth"     #  7.1
#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250729_043614/best_acc_top1_epoch_81.pth"     #  8
CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250730_121806_padding/best_acc_top1_epoch_54.pth"     #  10 padding Best
#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250801_100506_R50_512/best_acc_top1_epoch_66.pth"     #  11 R50-512 padding 需要修改配置文件和模型文件
#CHECKPOINT="${script_pth}/tools/work_dirs/pose_rgb_fusion/20250805_045639/best_acc_top1_epoch_72.pth"     #  12 padding
CONFIG="$video_list/multimodal_poseGCN-rgbR50_fusion.py"
CHECKPOINT=$CHECKPOINT CUDA_DEVICES=$CUDA_DEVICES CONFIG=$CONFIG PORT=29592 \
bash ${script_pth}/tools/dist_test.sh

# 4. Match Or Copy ||  处理后,删除路径中的所有空文件夹
python $script_pth/Select_samples.py --datasets $video_list --split $Method --score_thre $score_thre  --empty_pth $data_pth
#

echo $data_pth
echo 'okk.'
