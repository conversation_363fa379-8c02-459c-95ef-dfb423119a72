#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Focal Loss相关组件的脚本
"""

import sys
import os.path as osp
sys.path.insert(0, osp.join(osp.dirname(osp.abspath(__file__)), '..'))

import torch
import numpy as np
from mmengine.config import Config

def test_focal_loss():
    """测试Focal Loss实现"""
    print("=" * 50)
    print("测试 Focal Loss 实现")
    print("=" * 50)
    
    try:
        from mmaction.models.losses.focal_loss import FocalLoss, FocalLossWithSmoothing
        
        # 测试基础Focal Loss
        class_counts = [6258, 5494, 4751]
        focal_loss = FocalLoss(alpha='auto', gamma=2.0, class_counts=class_counts)
        
        # 创建测试数据
        batch_size = 32
        num_classes = 3
        inputs = torch.randn(batch_size, num_classes)
        targets = torch.randint(0, num_classes, (batch_size,))
        
        # 计算损失
        loss = focal_loss(inputs, targets)
        print(f"✓ FocalLoss 测试通过，损失值: {loss.item():.4f}")
        
        # 测试带Label Smoothing的Focal Loss
        focal_loss_smooth = FocalLossWithSmoothing(
            alpha='auto', 
            gamma=2.0, 
            label_smoothing=0.1,
            class_counts=class_counts
        )
        
        loss_smooth = focal_loss_smooth(inputs, targets)
        print(f"✓ FocalLossWithSmoothing 测试通过，损失值: {loss_smooth.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Focal Loss 测试失败: {e}")
        return False


def test_class_weight_utils():
    """测试类别权重计算工具"""
    print("\n" + "=" * 50)
    print("测试类别权重计算工具")
    print("=" * 50)
    
    try:
        from mmaction.utils.class_weight_utils import (
            calculate_dynamic_class_weights,
            get_class_distribution_stats,
            calculate_sample_difficulty
        )
        
        # 测试动态权重计算
        class_counts = [6258, 5494, 4751]
        weights = calculate_dynamic_class_weights(class_counts)
        print(f"✓ 动态权重计算: {weights}")
        
        # 测试分布统计
        stats = get_class_distribution_stats(class_counts)
        print(f"✓ 分布统计: 总样本={stats['total_samples']}, 不平衡比例={stats['imbalance_ratio']:.2f}")
        
        # 测试样本难度计算
        predictions = torch.randn(32, 3)
        targets = torch.randint(0, 3, (32,))
        difficulty_scores, hard_indices = calculate_sample_difficulty(predictions, targets)
        print(f"✓ 样本难度计算: 困难样本数量={len(hard_indices)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 类别权重工具测试失败: {e}")
        return False


def test_config_loading():
    """测试配置文件加载"""
    print("\n" + "=" * 50)
    print("测试配置文件加载")
    print("=" * 50)
    
    try:
        config_path = 'configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_focal.py'
        cfg = Config.fromfile(config_path)
        
        print(f"✓ 配置文件加载成功")
        print(f"✓ 损失函数类型: {cfg.model.cls_head.loss_cls.type}")
        print(f"✓ Focal Loss参数: alpha={cfg.model.cls_head.loss_cls.alpha}, gamma={cfg.model.cls_head.loss_cls.gamma}")
        print(f"✓ Label smoothing: {cfg.model.cls_head.loss_cls.label_smoothing}")
        print(f"✓ 自定义Hook数量: {len(cfg.custom_hooks)}")
        
        # 检查Hook配置
        hook_types = [hook['type'] for hook in cfg.custom_hooks]
        print(f"✓ Hook类型: {hook_types}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置文件测试失败: {e}")
        return False


def test_dataset_class_counts():
    """测试数据集类别统计功能"""
    print("\n" + "=" * 50)
    print("测试数据集类别统计")
    print("=" * 50)
    
    try:
        from mmaction.datasets.multimodal_dataset import PoseRgbDataset
        
        # 创建模拟数据
        mock_data_list = [
            {'label': 0}, {'label': 1}, {'label': 2},
            {'label': 0}, {'label': 1}, {'label': 0}
        ]
        
        # 创建数据集实例（模拟）
        dataset = PoseRgbDataset.__new__(PoseRgbDataset)
        dataset.data_list = mock_data_list
        
        # 测试类别统计
        class_counts = dataset.get_class_counts()
        print(f"✓ 类别统计功能正常: {class_counts}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据集测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试 Focal Loss 优化组件...")
    
    # 注册所有模块
    try:
        from mmaction.utils import register_all_modules
        register_all_modules()
        print("✓ 模块注册成功")
    except Exception as e:
        print(f"✗ 模块注册失败: {e}")
        return
    
    # 运行所有测试
    tests = [
        test_focal_loss,
        test_class_weight_utils,
        test_config_loading,
        test_dataset_class_counts
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"✗ 测试 {test_func.__name__} 出现异常: {e}")
            results.append(False)
    
    # 总结测试结果
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！Focal Loss优化组件已准备就绪。")
        print("\n可以使用以下命令开始训练:")
        print("python tools/train_focal_loss.py configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_focal.py")
    else:
        print("⚠️  部分测试失败，请检查相关组件。")
    
    return passed == total


if __name__ == '__main__':
    main()
