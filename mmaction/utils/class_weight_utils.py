import numpy as np
from typing import List, Optional, Dict, Tuple
import torch


def calculate_dynamic_class_weights(class_counts: List[int], 
                                  target_ratios: Optional[List[float]] = None,
                                  min_weight: float = 0.5,
                                  max_weight: float = 3.0) -> List[float]:
    """
    动态计算类别权重
    
    Args:
        class_counts: 各类别样本数量 [normal_count, fake_count, head_err_count]
        target_ratios: 目标权重比例，默认[1.0, 1.2, 1.1]
        min_weight: 最小权重限制
        max_weight: 最大权重限制
    
    Returns:
        List[float]: 计算得到的类别权重
    """
    if target_ratios is None:
        target_ratios = [1.0, 1.2, 1.1]  # 对作弊类别给予更高权重
    
    total_samples = sum(class_counts)
    num_classes = len(class_counts)

    print(class_counts)
    
    weights = []
    for count, ratio in zip(class_counts, target_ratios):
        print(count, num_classes)
        # 基础权重：与样本数成反比
        base_weight = total_samples / (num_classes * count)
        # 调整权重：结合目标比例
        adjusted_weight = base_weight * ratio
        # 应用权重限制
        adjusted_weight = max(min_weight, min(max_weight, adjusted_weight))
        weights.append(adjusted_weight)
    
    return weights


def calculate_class_weights_from_confusion_matrix(confusion_matrix: np.ndarray,
                                                target_metrics: Dict[str, float],
                                                current_weights: List[float],
                                                adjustment_factor: float = 0.1) -> List[float]:
    """
    基于混淆矩阵和目标指标调整类别权重
    
    Args:
        confusion_matrix: 混淆矩阵，shape: (num_classes, num_classes)
        target_metrics: 目标指标 {'normal_precision': 0.985, 'abnormal_recall': 0.97}
        current_weights: 当前类别权重
        adjustment_factor: 调整因子，控制权重变化幅度
    
    Returns:
        List[float]: 调整后的类别权重
    """
    num_classes = confusion_matrix.shape[0]
    new_weights = current_weights.copy()
    
    # 计算当前指标
    precision = np.diag(confusion_matrix) / (np.sum(confusion_matrix, axis=0) + 1e-8)
    recall = np.diag(confusion_matrix) / (np.sum(confusion_matrix, axis=1) + 1e-8)
    
    # 正常类别（类别0）的精确率调整
    if 'normal_precision' in target_metrics:
        target_normal_precision = target_metrics['normal_precision']
        current_normal_precision = precision[0]
        
        if current_normal_precision < target_normal_precision:
            # 精确率低，需要降低对正常类别的惩罚
            new_weights[0] *= (1 - adjustment_factor)
        elif current_normal_precision > target_normal_precision + 0.01:
            # 精确率过高，可以适当增加惩罚
            new_weights[0] *= (1 + adjustment_factor * 0.5)
    
    # 异常类别（类别1,2）的召回率调整
    if 'abnormal_recall' in target_metrics:
        target_abnormal_recall = target_metrics['abnormal_recall']
        
        for i in [1, 2]:  # 作弊和未抱头类别
            current_recall = recall[i]
            
            if current_recall < target_abnormal_recall:
                # 召回率低，需要增加对异常类别的关注
                new_weights[i] *= (1 + adjustment_factor)
            elif current_recall > target_abnormal_recall + 0.02:
                # 召回率过高，可以适当降低权重
                new_weights[i] *= (1 - adjustment_factor * 0.5)
    
    # 应用权重限制
    min_weight, max_weight = 0.5, 3.0
    new_weights = [max(min_weight, min(max_weight, w)) for w in new_weights]
    
    return new_weights


def get_class_distribution_stats(class_counts: List[int]) -> Dict[str, float]:
    """
    获取类别分布统计信息
    
    Args:
        class_counts: 各类别样本数量
    
    Returns:
        Dict[str, float]: 统计信息
    """
    total_samples = sum(class_counts)
    num_classes = len(class_counts)
    
    stats = {
        'total_samples': total_samples,
        'num_classes': num_classes,
        'class_ratios': [count / total_samples for count in class_counts],
        'imbalance_ratio': max(class_counts) / min(class_counts),
        'entropy': -sum((count / total_samples) * np.log(count / total_samples + 1e-8) 
                       for count in class_counts)
    }
    
    return stats


def calculate_sample_difficulty(predictions: torch.Tensor, 
                              targets: torch.Tensor,
                              difficulty_threshold: float = 0.7) -> Tuple[torch.Tensor, List[int]]:
    """
    计算样本难度，识别困难样本
    
    Args:
        predictions: 模型预测概率，shape: (N, C)
        targets: 真实标签，shape: (N,)
        difficulty_threshold: 困难样本阈值
    
    Returns:
        Tuple[torch.Tensor, List[int]]: (样本难度分数, 困难样本索引)
    """
    # 计算预测置信度
    probs = torch.softmax(predictions, dim=1)
    confidence = probs.gather(1, targets.unsqueeze(1)).squeeze(1)
    
    # 计算难度分数（置信度越低，难度越高）
    difficulty_scores = 1.0 - confidence
    
    # 识别困难样本
    hard_sample_mask = confidence < difficulty_threshold
    hard_sample_indices = torch.where(hard_sample_mask)[0].tolist()
    
    return difficulty_scores, hard_sample_indices


def update_class_weights_with_validation_metrics(current_weights: List[float],
                                               val_metrics: Dict[str, float],
                                               target_metrics: Dict[str, float],
                                               learning_rate: float = 0.1) -> List[float]:
    """
    基于验证集指标更新类别权重
    
    Args:
        current_weights: 当前类别权重
        val_metrics: 验证集指标
        target_metrics: 目标指标
        learning_rate: 权重更新学习率
    
    Returns:
        List[float]: 更新后的类别权重
    """
    new_weights = current_weights.copy()
    
    # 异常检出率调整
    if 'abnormal_detection_rate' in val_metrics and 'abnormal_detection_rate' in target_metrics:
        current_rate = val_metrics['abnormal_detection_rate']
        target_rate = target_metrics['abnormal_detection_rate']
        
        if current_rate < target_rate:
            # 检出率低，增加异常类别权重
            new_weights[1] *= (1 + learning_rate * (target_rate - current_rate))
            new_weights[2] *= (1 + learning_rate * (target_rate - current_rate))
    
    # 正常误判率调整
    if 'normal_false_positive_rate' in val_metrics and 'normal_false_positive_rate' in target_metrics:
        current_rate = val_metrics['normal_false_positive_rate']
        target_rate = target_metrics['normal_false_positive_rate']
        
        if current_rate > target_rate:
            # 误判率高，降低正常类别权重
            new_weights[0] *= (1 - learning_rate * (current_rate - target_rate))
    
    # 应用权重限制
    min_weight, max_weight = 0.5, 3.0
    new_weights = [max(min_weight, min(max_weight, w)) for w in new_weights]
    
    return new_weights
