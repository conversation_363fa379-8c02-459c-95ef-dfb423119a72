import cv2
import numpy as np
import os
import shutil
import glob
import random
from typing import List, <PERSON><PERSON>
from flask import Flask, render_template, request, jsonify, send_from_directory

app = Flask(__name__)

# --- 常量 ---
BASE_DIR = "/media/pyl/WD_Blue_1T/All_proj/classify_cheat/pose_rgb"
INPUT_DIR = os.path.join(BASE_DIR, "0_normal")
OUTPUT_DIR = os.path.join(BASE_DIR, "Fake_AI")
TOOL_DIR = os.path.join(BASE_DIR, "Fake_tool")
TOOL_MASKS_DIR = os.path.join(BASE_DIR, "Fake_tool_masks")


# --- 路由 ---


@app.route("/")
def index():
    """提供主页面"""
    return render_template("index.html")


@app.route("/image/<path:image_path>")
def serve_image(image_path: str):
    """安全地提供图片文件"""
    # 此处需要一个绝对安全的文件路径处理，暂时使用最基础的
    # 注意：生产环境中需要更严格的路径校验
    return send_from_directory(BASE_DIR, image_path)


@app.route("/api/source_images")
def get_source_images():
    """获取所有可用于创建蒙版的源图片"""
    source_images_paths = glob.glob(os.path.join(TOOL_DIR, "**", "*.*"), recursive=True)
    source_images = [
        os.path.relpath(p, BASE_DIR)
        for p in source_images_paths
        if p.lower().endswith((".png", ".jpg", ".jpeg"))
    ]
    return jsonify(source_images)


@app.route("/api/create_mask", methods=["POST"])
def create_mask_endpoint():
    """接收坐标和图片路径，创建并保存蒙版"""
    data = request.json
    image_path_relative = data.get("image_path")
    poly_pts = data.get("points")

    if not image_path_relative or not poly_pts or len(poly_pts) < 3:
        return jsonify({"status": "error", "message": "无效的输入数据"}), 400

    image_path_full = os.path.join(BASE_DIR, image_path_relative)

    image = cv2.imread(image_path_full)
    if image is None:
        return jsonify({"status": "error", "message": "无法读取图片"}), 404

    try:
        mask = np.zeros(image.shape[:2], dtype=np.uint8)
        # 修正: 将浮点坐标转换为OpenCV要求的int32类型
        pts = np.array(poly_pts, dtype=np.int32)
        cv2.fillPoly(mask, [pts], 255)

        b, g, r = cv2.split(image)
        alpha = mask
        result = cv2.merge([b, g, r, alpha])

        output_filename = (
            os.path.splitext(os.path.basename(image_path_full))[0] + "_mask.png"
        )
        output_path = os.path.join(TOOL_MASKS_DIR, output_filename)
        cv2.imwrite(output_path, result)

        return jsonify({"status": "success", "message": f"蒙版已保存到 {output_path}"})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/api/normal_samples")
def get_normal_samples():
    """获取所有待处理的正常样本图片"""
    sample_dirs = [
        d for d in glob.glob(os.path.join(INPUT_DIR, "**", "*")) if os.path.isdir(d)
    ]
    all_jpgs = []
    for sample_dir in sample_dirs:
        jpg_files = sorted(glob.glob(os.path.join(sample_dir, "*.jpg")))
        # 为前端准备好数据结构
        if len(jpg_files) == 3:
            all_jpgs.append(
                {
                    "sample_name": os.path.basename(sample_dir),
                    "images": [os.path.relpath(p, BASE_DIR) for p in jpg_files],
                }
            )
    return jsonify(all_jpgs)


@app.route("/api/tool_masks")
def get_tool_masks():
    """获取所有可用的工具蒙版"""
    mask_paths = glob.glob(os.path.join(TOOL_MASKS_DIR, "*.png"))
    masks_data = []
    for p in mask_paths:
        masks_data.append(
            {"name": os.path.basename(p), "path": os.path.relpath(p, BASE_DIR)}
        )
    return jsonify(masks_data)


@app.route("/api/generate_sample", methods=["POST"])
def generate_sample_endpoint():
    data = request.json
    image_path_relative = data.get("image_path")
    keypoints = data.get("points")
    tool_mask_path_relative = data.get("tool_mask")

    # 新增：获取动态参数，提供默认值
    parameters = data.get("parameters", {})
    blend_strength = parameters.get("blend_strength", 0.6)
    fusion_mode = parameters.get("fusion_mode", "NORMAL_CLONE")
    seamless_weight = parameters.get("seamless_weight", 0.6)
    mask_opacity = parameters.get("mask_opacity", 1.0)
    brightness_adjust = parameters.get("brightness_adjust", 0)
    contrast_adjust = parameters.get("contrast_adjust", 1.0)

    if (
        not all([image_path_relative, keypoints, tool_mask_path_relative])
        or len(keypoints) != 2
    ):
        return jsonify({"status": "error", "message": "无效的输入数据"}), 400

    image_path_full = os.path.join(BASE_DIR, image_path_relative)
    tool_mask_path_full = os.path.join(BASE_DIR, tool_mask_path_relative)

    background_img = cv2.imread(image_path_full)
    tool_img = cv2.imread(tool_mask_path_full, cv2.IMREAD_UNCHANGED)

    if background_img is None or tool_img is None:
        return jsonify({"status": "error", "message": "无法读取图片文件"}), 404

    try:
        # --- 新增：自动裁剪工具蒙版以适应内容 ---
        alpha_ch = tool_img[:, :, 3]
        non_zero_pts = cv2.findNonZero(alpha_ch)
        if non_zero_pts is None:
            return jsonify({"status": "error", "message": "工具蒙版为空或全透明"}), 400
        x, y, w, h = cv2.boundingRect(non_zero_pts)
        cropped_tool_img = tool_img[y : y + h, x : x + w]
        # --- 裁剪结束 ---

        # --- 全新的、基于几何中心的变换逻辑 ---
        tool_h, tool_w, _ = cropped_tool_img.shape

        # 1. 计算目标位置的几何属性
        p1 = np.float32(keypoints[0])
        p2 = np.float32(keypoints[1])

        # 源图中的三个点（定义工具的局部坐标系）：左中、右中、上中
        src_pts = np.float32(
            [
                [0, tool_h / 2],
                [tool_w, tool_h / 2],
                [tool_w / 2, 0],
            ]
        )

        # 目标图中的对应三点：p1（左端）、p2（右端）、p3（向上的法向方向，防止上下镜像）
        dx = p2[0] - p1[0]
        dy = p2[1] - p1[1]
        distance = float(np.hypot(dx, dy)) if (dx != 0 or dy != 0) else 1.0
        # 单位切向量（宽度方向）
        tx_u = dx / distance
        ty_u = dy / distance
        # 单位法向量（高度向“上”方向），Canvas Y 轴向下，因此“上”为负Y，需要沿(-ty, tx)定义
        nx_u = -ty_u
        ny_u = tx_u
        # 目标的上中点：线段中点沿法向正方向移动 tool_h/2
        mid = (p1 + p2) / 2.0
        p3 = np.float32(
            [mid[0] + nx_u * (tool_h / 2.0), mid[1] + ny_u * (tool_h / 2.0)]
        )

        dst_pts = np.float32([p1, p2, p3])
        M = cv2.getAffineTransform(src_pts, dst_pts)

        # 4. 应用最终的变换
        transformed_tool = cv2.warpAffine(
            cropped_tool_img, M, (background_img.shape[1], background_img.shape[0])
        )

        # --- 使用动态参数进行融合处理 ---
        alpha_channel = transformed_tool[:, :, 3]

        # 应用透明度参数
        alpha_channel = (alpha_channel * mask_opacity).astype(np.uint8)

        mask_full = np.where(alpha_channel > 0, 255, 0).astype(np.uint8)
        non_zero = cv2.findNonZero(mask_full)
        if non_zero is None:
            return jsonify({"status": "error", "message": "变换后的工具蒙版为空"}), 400
        x2, y2, w2, h2 = cv2.boundingRect(non_zero)
        src_roi: np.ndarray = transformed_tool[y2 : y2 + h2, x2 : x2 + w2, :3]
        mask_roi: np.ndarray = mask_full[y2 : y2 + h2, x2 : x2 + w2]
        center_point: Tuple[int, int] = (int(x2 + w2 / 2), int(y2 + h2 / 2))

        # 应用亮度和对比度调整到前景
        if brightness_adjust != 0 or contrast_adjust != 1.0:
            src_roi = src_roi.astype(np.float32)
            if brightness_adjust != 0:
                src_roi += brightness_adjust
            if contrast_adjust != 1.0:
                src_roi = (src_roi - 128) * contrast_adjust + 128
            src_roi = np.clip(src_roi, 0, 255).astype(np.uint8)

        # 防御：如果ROI尺寸异常或掩码为空，回退到原始alpha混合
        if src_roi.size == 0 or mask_roi.size == 0:
            alpha_3ch = (alpha_channel / 255.0)[..., None]
            foreground = transformed_tool[:, :, :3]
            result = (1 - alpha_3ch) * background_img + alpha_3ch * foreground
            processed_image = result.astype(np.uint8)
        else:
            try:
                # 根据参数选择融合模式
                clone_flag = (
                    cv2.NORMAL_CLONE
                    if fusion_mode == "NORMAL_CLONE"
                    else cv2.MIXED_CLONE
                )

                sc_image = cv2.seamlessClone(
                    src_roi, background_img, mask_roi, center_point, clone_flag
                )

                # 与经典 alpha 混合做加权融合，使用动态参数
                alpha_3ch = (alpha_channel / 255.0)[..., None]
                foreground = transformed_tool[:, :, :3]
                alpha_blend = (1 - alpha_3ch) * background_img + alpha_3ch * foreground

                processed_image = cv2.addWeighted(
                    sc_image,
                    seamless_weight,
                    alpha_blend.astype(np.uint8),
                    1.0 - seamless_weight,
                    0,
                )
            except Exception as e:
                print(f"Poisson融合失败，回退到alpha混合: {e}")
                # 回退到alpha混合
                alpha_3ch = (alpha_channel / 255.0)[..., None]
                foreground = transformed_tool[:, :, :3]
                processed_image = (
                    (1 - alpha_3ch) * background_img + alpha_3ch * foreground
                ).astype(np.uint8)
        # --- 融合结束 ---

        # 保存并拷贝元数据
        sample_dir_name = os.path.basename(os.path.dirname(image_path_full))
        output_dir_sample = os.path.join(OUTPUT_DIR, sample_dir_name)
        os.makedirs(output_dir_sample, exist_ok=True)

        output_path = os.path.join(output_dir_sample, os.path.basename(image_path_full))
        cv2.imwrite(output_path, processed_image)

        # 仅在处理每个样本的第一张图片时拷贝元数据，避免重复
        if (
            os.path.basename(image_path_full)
            == sorted(os.listdir(os.path.dirname(image_path_full)))[0]
        ):
            original_sample_dir = os.path.dirname(image_path_full)
            for ext in ["*.avi", "*.pkl"]:
                for meta_file in glob.glob(os.path.join(original_sample_dir, ext)):
                    shutil.copy(meta_file, output_dir_sample)

        return jsonify(
            {
                "status": "success",
                "message": f"样本已生成于 {output_path}",
                "generated_image_path": os.path.relpath(output_path, BASE_DIR),
            }
        )
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/api/realtime_preview", methods=["POST"])
def realtime_preview_endpoint():
    """生成实时预览图像（返回base64编码的图像）"""
    data = request.json
    image_path_relative = data.get("image_path")
    keypoints = data.get("points")
    tool_mask_path_relative = data.get("tool_mask")

    # 获取动态参数
    parameters = data.get("parameters", {})
    blend_strength = parameters.get("blend_strength", 0.6)
    fusion_mode = parameters.get("fusion_mode", "NORMAL_CLONE")
    seamless_weight = parameters.get("seamless_weight", 0.6)
    mask_opacity = parameters.get("mask_opacity", 1.0)
    brightness_adjust = parameters.get("brightness_adjust", 0)
    contrast_adjust = parameters.get("contrast_adjust", 1.0)

    # 参数验证
    if (
        not all([image_path_relative, keypoints, tool_mask_path_relative])
        or len(keypoints) != 2
    ):
        return jsonify({"status": "error", "message": "无效的输入数据"}), 400

    try:
        image_path_full = os.path.join(BASE_DIR, image_path_relative)
        tool_mask_path_full = os.path.join(BASE_DIR, tool_mask_path_relative)

        background_img = cv2.imread(image_path_full)
        tool_img = cv2.imread(tool_mask_path_full, cv2.IMREAD_UNCHANGED)

        if background_img is None or tool_img is None:
            return jsonify({"status": "error", "message": "无法读取图片文件"}), 404

        # 复用generate_sample_endpoint的完整处理逻辑
        # --- 自动裁剪工具蒙版以适应内容 ---
        alpha_ch = tool_img[:, :, 3]
        non_zero_pts = cv2.findNonZero(alpha_ch)
        if non_zero_pts is None:
            return jsonify({"status": "error", "message": "工具蒙版为空或全透明"}), 400
        x, y, w, h = cv2.boundingRect(non_zero_pts)
        cropped_tool_img = tool_img[y : y + h, x : x + w]

        # --- 基于几何中心的变换逻辑 ---
        tool_h, tool_w, _ = cropped_tool_img.shape
        p1 = np.float32(keypoints[0])
        p2 = np.float32(keypoints[1])

        src_pts = np.float32(
            [
                [0, tool_h / 2],
                [tool_w, tool_h / 2],
                [tool_w / 2, 0],
            ]
        )

        dx = p2[0] - p1[0]
        dy = p2[1] - p1[1]
        distance = float(np.hypot(dx, dy)) if (dx != 0 or dy != 0) else 1.0
        tx_u = dx / distance
        ty_u = dy / distance
        nx_u = -ty_u
        ny_u = tx_u
        mid = (p1 + p2) / 2.0
        p3 = np.float32(
            [mid[0] + nx_u * (tool_h / 2.0), mid[1] + ny_u * (tool_h / 2.0)]
        )

        dst_pts = np.float32([p1, p2, p3])
        M = cv2.getAffineTransform(src_pts, dst_pts)

        transformed_tool = cv2.warpAffine(
            cropped_tool_img, M, (background_img.shape[1], background_img.shape[0])
        )

        # --- 使用动态参数进行融合处理 ---
        alpha_channel = transformed_tool[:, :, 3]
        alpha_channel = (alpha_channel * mask_opacity).astype(np.uint8)

        mask_full = np.where(alpha_channel > 0, 255, 0).astype(np.uint8)
        non_zero = cv2.findNonZero(mask_full)
        if non_zero is None:
            return jsonify({"status": "error", "message": "变换后的工具蒙版为空"}), 400
        x2, y2, w2, h2 = cv2.boundingRect(non_zero)
        src_roi: np.ndarray = transformed_tool[y2 : y2 + h2, x2 : x2 + w2, :3]
        mask_roi: np.ndarray = mask_full[y2 : y2 + h2, x2 : x2 + w2]
        center_point: Tuple[int, int] = (int(x2 + w2 / 2), int(y2 + h2 / 2))

        # 应用亮度和对比度调整
        if brightness_adjust != 0 or contrast_adjust != 1.0:
            src_roi = src_roi.astype(np.float32)
            if brightness_adjust != 0:
                src_roi += brightness_adjust
            if contrast_adjust != 1.0:
                src_roi = (src_roi - 128) * contrast_adjust + 128
            src_roi = np.clip(src_roi, 0, 255).astype(np.uint8)

        # 图像融合
        if src_roi.size == 0 or mask_roi.size == 0:
            alpha_3ch = (alpha_channel / 255.0)[..., None]
            foreground = transformed_tool[:, :, :3]
            result = (1 - alpha_3ch) * background_img + alpha_3ch * foreground
            processed_image = result.astype(np.uint8)
        else:
            try:
                clone_flag = (
                    cv2.NORMAL_CLONE
                    if fusion_mode == "NORMAL_CLONE"
                    else cv2.MIXED_CLONE
                )
                sc_image = cv2.seamlessClone(
                    src_roi, background_img, mask_roi, center_point, clone_flag
                )

                alpha_3ch = (alpha_channel / 255.0)[..., None]
                foreground = transformed_tool[:, :, :3]
                alpha_blend = (1 - alpha_3ch) * background_img + alpha_3ch * foreground

                processed_image = cv2.addWeighted(
                    sc_image,
                    seamless_weight,
                    alpha_blend.astype(np.uint8),
                    1.0 - seamless_weight,
                    0,
                )
            except Exception as e:
                print(f"Poisson融合失败，回退到alpha混合: {e}")
                alpha_3ch = (alpha_channel / 255.0)[..., None]
                foreground = transformed_tool[:, :, :3]
                processed_image = (
                    (1 - alpha_3ch) * background_img + alpha_3ch * foreground
                ).astype(np.uint8)

        # 将图像编码为base64
        import base64

        _, buffer = cv2.imencode(
            ".jpg", processed_image, [cv2.IMWRITE_JPEG_QUALITY, 85]
        )
        img_base64 = base64.b64encode(buffer).decode("utf-8")

        return jsonify(
            {
                "status": "success",
                "message": "实时预览生成成功",
                "preview_image": f"data:image/jpeg;base64,{img_base64}",
            }
        )

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500


if __name__ == "__main__":
    # 确保所有需要的目录都存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    os.makedirs(TOOL_MASKS_DIR, exist_ok=True)
    app.run(debug=True, port=5001)
