#%%
import copy as cp
from collections import OrderedDict

import torch
from mmengine.runner.checkpoint import _load_checkpoint

from mmaction.utils import register_all_modules
from mmaction.registry import MODELS
#%%
backbone_cfg = dict(
    type='RGBPoseConv3D',
    speed_ratio=4,
    channel_ratio=4,
    rgb_pathway=dict(
        num_stages=4,
        lateral=True,
        lateral_infl=1,
        lateral_activate=[0, 0, 1, 1],
        fusion_kernel=7,
        base_channels=64,
        conv1_kernel=(1, 7, 7),
        inflate=(0, 0, 1, 1),
        with_pool2=False),
    pose_pathway=dict(
        num_stages=3,
        stage_blocks=(4, 6, 3),
        lateral=True,
        lateral_inv=True,
        lateral_infl=16,
        lateral_activate=(0, 1, 1),
        fusion_kernel=7,
        in_channels=17,
        base_channels=32,
        out_indices=(2, ),
        conv1_kernel=(1, 7, 7),
        conv1_stride_s=1,
        conv1_stride_t=1,
        pool1_stride_s=1,
        pool1_stride_t=1,
        inflate=(0, 1, 1),
        spatial_strides=(2, 2, 2),
        temporal_strides=(1, 1, 1),
        dilations=(1, 1, 1),
        with_pool2=False))
head_cfg = dict(
    type='RGBPoseHead',
    num_classes=60,
    in_channels=[2048, 512],
    average_clips='prob')
model_cfg = dict(
    type='Recognizer3D',
    backbone=backbone_cfg,
    cls_head=head_cfg)

register_all_modules()
model = MODELS.build(model_cfg)
#%%
# set your paths of the pretrained weights here
rgb_filepath = 'https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/rgbpose_conv3d/rgb_only_20230228-576b9f86.pth'
pose_filepath = 'https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/rgbpose_conv3d/pose_only_20230228-fa40054e.pth'
#%%
rgb_ckpt = _load_checkpoint(rgb_filepath, map_location='cpu')['state_dict']
pose_ckpt = _load_checkpoint(pose_filepath, map_location='cpu')['state_dict']
#%%
rgb_ckpt = {k.replace('backbone', 'backbone.rgb_path').replace('fc_cls', 'fc_rgb'): v for k, v in rgb_ckpt.items()}
pose_ckpt = {k.replace('backbone', 'backbone.pose_path').replace('fc_cls', 'fc_pose'): v for k, v in pose_ckpt.items()}
#%%
old_ckpt = {}
old_ckpt.update(rgb_ckpt)
old_ckpt.update(pose_ckpt)
#%%
# The difference is in dim-1
def padding(weight, new_shape):
    new_weight = weight.new_zeros(new_shape)
    new_weight[:, :weight.shape[1]] = weight
    return new_weight
#%%
ckpt = cp.deepcopy(old_ckpt)
name = 'backbone.rgb_path.layer3.0.conv1.conv.weight'
ckpt[name] = padding(ckpt[name], (256, 640, 3, 1, 1))
name = 'backbone.rgb_path.layer3.0.downsample.conv.weight'
ckpt[name] = padding(ckpt[name], (1024, 640, 1, 1, 1))
name = 'backbone.rgb_path.layer4.0.conv1.conv.weight'
ckpt[name] = padding(ckpt[name], (512, 1280, 3, 1, 1))
name = 'backbone.rgb_path.layer4.0.downsample.conv.weight'
ckpt[name] = padding(ckpt[name], (2048, 1280, 1, 1, 1))
name = 'backbone.pose_path.layer2.0.conv1.conv.weight'
ckpt[name] = padding(ckpt[name], (64, 160, 3, 1, 1))
name = 'backbone.pose_path.layer2.0.downsample.conv.weight'
ckpt[name] = padding(ckpt[name], (256, 160, 1, 1, 1))
name = 'backbone.pose_path.layer3.0.conv1.conv.weight'
ckpt[name] = padding(ckpt[name], (128, 320, 3, 1, 1))
name = 'backbone.pose_path.layer3.0.downsample.conv.weight'
ckpt[name] = padding(ckpt[name], (512, 320, 1, 1, 1))
ckpt = OrderedDict(ckpt)
torch.save({'state_dict': ckpt}, 'rgbpose_conv3d_init.pth')
#%%
model.load_state_dict(ckpt, strict=False)