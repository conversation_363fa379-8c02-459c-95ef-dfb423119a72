Collections:
- Name: BMN
  README: configs/localization/bmn/README.md
  Paper:
    URL: https://arxiv.org/abs/1907.09702
    Title: "BMN: Boundary-Matching Network for Temporal Action Proposal Generation"

Models:
  - Name: bmn_2xb8-400x100-9e_activitynet-feature
    Config: configs/localization/bmn/bmn_2xb8-400x100-9e_activitynet-feature.py
    In Collection: BMN
    Metadata:
      Batch Size: 8
      Epochs: 9
      Training Data: ActivityNet v1.3
      Training Resources: 2 GPUs
      feature: cuhk_mean_100
    Modality: RGB
    Results:
      - Dataset: ActivityNet v1.3
        Task: Temporal Action Localization
        Metrics:
              AUC: 67.25
              AR@1: 32.89
              AR@5: 49.43
              AR@10: 56.64
              AR@100: 75.29
    Training Log: https://download.openmmlab.com/mmaction/v1.0/localization/bmn/bmn_2xb8-400x100-9e_activitynet-feature.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/localization/bmn/bmn_2xb8-400x100-9e_activitynet-feature_20220908-79f92857.pth
