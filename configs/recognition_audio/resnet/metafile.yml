Collections:
  - Name: Audio
    README: configs/recognition_audio/resnet/README.md
    Paper:
      URL: https://arxiv.org/abs/2001.08740
      Title: "Audiovisual SlowFast Networks for Video Recognition"

Models:
  - Name: tsn_r18_8xb320-64x1x1-100e_kinetics400-audio-feature
    Config: configs/recognition_audio/resnet/tsn_r18_8xb320-64x1x1-100e_kinetics400-audio-feature.py
    In Collection: Audio
    Metadata:
      Architecture: ResNet18
      Batch Size: 320
      Epochs: 100
      FLOPs: 0.37G
      Parameters: 11.4M
      Pretrained: None
      n_fft: 1024
      Training Data: Kinetics-400
      Training Resources: 8 GPUs
    Modality: Audio
    Results:
      - Dataset: Kinetics-400
        Task: Action Recognition
        Metrics:
          Top 1 Accuracy: 13.7
          Top 5 Accuracy: 27.3
    Training Log: https://download.openmmlab.com/mmaction/v1.0/recognition_audio/resnet/tsn_r18_8xb320-64x1x1-100e_kinetics400-audio-feature/tsn_r18_8xb320-64x1x1-100e_kinetics400-audio-feature.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/recognition_audio/resnet/tsn_r18_8xb320-64x1x1-100e_kinetics400-audio-feature/tsn_r18_8xb320-64x1x1-100e_kinetics400-audio-feature_20230702-e4642fb0.pth
