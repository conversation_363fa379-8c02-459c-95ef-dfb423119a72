# 方案H：分类+重构联合损失（Anomaly-aware）
# 基于 warmup 配置，替换分类头为 TwoFusionReconHead，并将类别改为4类

import os

default_scope = 'mmaction'

default_hooks = dict(
    runtime_info=dict(type='RuntimeInfoHook'),
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=20, ignore_last=False),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(type='CheckpointHook', interval=1, save_best='auto'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'))

env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0),
    dist_cfg=dict(backend='nccl'))

log_processor = dict(type='LogProcessor', window_size=20, by_epoch=True)
vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(type='ActionVisualizer', vis_backends=vis_backends)

log_level = 'INFO'
load_from = None
resume = False

# ---------------------------------------------------------------------------
# 自定义导入：注册 TwoFusionReconHead
custom_imports = dict(
    imports=['mmaction.models.heads.twofusion_recon_head'], allow_failed_imports=False)

# ---------------------------------------------------------------------------

num_classes = 4
split_label = {0: [1, 2, 3], 1: [0]}   # 0:异常(含作弊/未抱头/不标准) -> 二类报告用

model = dict(
    type='MultiModalRecognizer',
    pose_backbone=dict(
        type='STGCN',
        in_channels=3,
        graph_cfg=dict(layout='coco', mode='stgcn_spatial'),
        init_cfg=dict(type='Pretrained', checkpoint='../stgcn_8xb16-bone-u100-80e_ntu120-xsub-keypoint-2d_20221129-131e63c3.pth')
    ),
    image_backbone=dict(
        type='MobileNetV2TSM',
        num_segments=3,
        is_shift=True,
        shift_div=8,
        pretrained2d=False,
        init_cfg=dict(type='Pretrained', checkpoint='../tsm_imagenet-pretrained-mobilenetv2_8xb16-1x1x8-100e_kinetics400-rgb_20230414-401127fd.pth'),
        out_indices=(6,),
        frozen_stages=-1,
        norm_eval=False,
    ),
    fusion_neck=dict(
        type='MultiModalFusionNeck',
        pose_feat_dim=256,
        img_feat_dim=320,
        fusion_dim=512,
        fusion_type='attention',
        dropout=0.6
    ),
    cls_head=dict(
        type='TwoFusionReconHead',
        num_classes=num_classes,
        in_channels=512,
        latent_dim=128,
        hidden_dim=256,
        dropout_ratio=0.5,
        recon_loss_weight=0.3,
        anomaly_label_index=3,     # 将第4类视为异常类
        anomaly_recon_weight=0.0,  # 异常样本重构权重为0
        loss_cls=dict(
            type='CrossEntropyLoss',
            loss_weight=1.0,
            class_weight=[1.0] * num_classes
        )
    ),
    train_cfg=None,
    test_cfg=dict(average_clips='prob')
)

dataset_type = 'PoseRgbDataset'
TrainVal_ann_file = '/media/pyl/WD_Blue_1T/All_proj/classify_cheat/Fusion_datasets/trainval_yolopose.pkl'
Test_ann_file = '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/test_trainval_yolopose.pkl'

train_pipeline = [
    dict(type='fusionPreNormalize2D', left_idx=11, right_idx=12),
    dict(type='GenSkeFeat', dataset='coco', feats=['b']),
    dict(type='UniformSampleFrames', clip_len=35, seed=255),
    dict(type='SimplifiedFormatGCNInput'),
    dict(type='SimplyResize', scale=(224, 224), use_padding=True, pad_val=(128, 128, 128), load_Resized_img=True),
    dict(type='ColorJitter'),
    dict(type='Flip', flip_ratio=0.5),
    dict(type='FormatShape', input_format='NCHW'),
    dict(type='PackActionInputs', collect_keys=('keypoint', 'imgs')),
]

val_pipeline = [
    dict(type='fusionPreNormalize2D', left_idx=11, right_idx=12),
    dict(type='GenSkeFeat', dataset='coco', feats=['b']),
    dict(type='UniformSampleFrames', clip_len=35, test_mode=True),
    dict(type='SimplifiedFormatGCNInput'),
    dict(type='SimplyResize', scale=(224, 224), use_padding=True, pad_val=(128, 128, 128), load_Resized_img=True),
    dict(type='FormatShape', input_format='NCHW'),
    dict(type='PackActionInputs', collect_keys=('keypoint', 'imgs')),
]

test_pipeline = [
    dict(type='fusionPreNormalize2D', left_idx=11, right_idx=12),
    dict(type='GenSkeFeat', dataset='coco', feats=['b']),
    dict(type='UniformSampleFrames', clip_len=35, test_mode=True),
    dict(type='SimplifiedFormatGCNInput'),
    dict(type='SimplyResize', scale=(224, 224), use_padding=True, pad_val=(128, 128, 128)),
    dict(type='FormatShape', input_format='NCHW'),
    dict(type='PackActionInputs_Test', collect_keys=('keypoint', 'imgs'),
         pred_txt=os.path.dirname(Test_ann_file), split_label=split_label),
]

train_dataloader = dict(
    batch_size=4,
    num_workers=0,
    sampler=dict(type='DefaultSampler', shuffle=True),
    dataset=dict(
        type=dataset_type,
        ann_file=TrainVal_ann_file,
        split='train',
        pipeline=train_pipeline))

val_dataloader = dict(
    batch_size=4,
    num_workers=0,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=TrainVal_ann_file,
        split='val',
        pipeline=val_pipeline,
        test_mode=True))

test_dataloader = dict(
    batch_size=64,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=Test_ann_file,
        split=None,
        pipeline=test_pipeline,
        test_mode=True))

val_evaluator = [dict(type='AccMetric')]
test_evaluator = [dict(type='ClassifyReport')]

train_cfg = dict(type='EpochBasedTrainLoop', max_epochs=100, val_begin=1, val_interval=1)
val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')

optim_wrapper = dict(
    type='OptimWrapper',
    optimizer=dict(type='AdamW', lr=0.001, betas=(0.9, 0.999), weight_decay=0.00015),
    paramwise_cfg=dict(
        custom_keys={
            'image_backbone': dict(lr_mult=0.1),
            'pose_backbone': dict(lr_mult=1.0),
            'fusion_neck': dict(lr_mult=1.0),
            'cls_head': dict(lr_mult=1.0)
        }
    ),
    clip_grad=dict(max_norm=10, norm_type=2),
)

param_scheduler = [
    dict(type='LinearLR', start_factor=0.001, by_epoch=True, begin=0, end=3),
    dict(type='LinearLR', start_factor=0.01, by_epoch=True, begin=3, end=8),
    dict(type='CosineAnnealingLR', T_max=92, eta_min=1e-6, by_epoch=True, begin=8, end=100)
]

work_dir = './work_dirs/pose_rgb_fusion_expH_recon'
find_unused_parameters = True


