# ==============================================================================
# ExpE: AMP+梯度累积（等效大batch）配置文件
# 通过自动混合精度训练和梯度累积实现等效大batch size训练
#
# 核心改进：
# - 启用AmpOptimWrapper（自动混合精度训练）
# - accumulative_counts=2（梯度累积，等效batch_size=8）
# - AdamW优化器（适合AMP训练）
# - clip_grad=10（适中的梯度裁剪）
# - 保持稳定的学习率策略
#
# 目标：增大等效batch、平滑梯度，提升训练稳定性
# 预期：减少内存使用，提升训练稳定性，保持精度
# ==============================================================================

import os

default_scope = "mmaction"

default_hooks = dict(
    runtime_info=dict(type="RuntimeInfoHook"),
    timer=dict(type="IterTimerHook"),
    logger=dict(type="LoggerHook", interval=20, ignore_last=False),
    param_scheduler=dict(type="ParamSchedulerHook"),
    checkpoint=dict(type="CheckpointHook", interval=1, save_best="auto"),
    sampler_seed=dict(type="DistSamplerSeedHook"),
    sync_buffers=dict(type="SyncBuffersHook"),
)

env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method="fork", opencv_num_threads=0),
    dist_cfg=dict(backend="nccl"),
)

log_processor = dict(type="LogProcessor", window_size=20, by_epoch=True)
vis_backends = [dict(type="LocalVisBackend")]
visualizer = dict(type="ActionVisualizer", vis_backends=vis_backends)

log_level = "INFO"
load_from = None
resume = False

# 基础配置
num_classes = 3
split_label = {0: [1, 2], 1: [0]}

# 模型配置
model = dict(
    type="MultiModalRecognizer",
    pose_backbone=dict(
        type="STGCN",
        in_channels=3,
        graph_cfg=dict(layout="coco", mode="stgcn_spatial"),
        init_cfg=dict(
            type="Pretrained",
            checkpoint="../stgcn_8xb16-bone-u100-80e_ntu120-xsub-keypoint-2d_20221129-131e63c3.pth",
        ),
    ),
    image_backbone=dict(
        type="MobileNetV2TSM",
        num_segments=3,
        is_shift=True,
        shift_div=8,
        pretrained2d=False,
        init_cfg=dict(
            type="Pretrained",
            checkpoint="../tsm_imagenet-pretrained-mobilenetv2_8xb16-1x1x8-100e_kinetics400-rgb_20230414-401127fd.pth",
        ),
        out_indices=(6,),
        frozen_stages=-1,
        norm_eval=False,
    ),
    fusion_neck=dict(
        type="MultiModalFusionNeck",
        pose_feat_dim=256,
        img_feat_dim=320,
        fusion_dim=512,
        fusion_type="attention",
        dropout=0.5,
    ),
    cls_head=dict(
        type="TwoFusionHead",
        num_classes=num_classes,
        in_channels=512,
        loss_cls=dict(
            type="CrossEntropyLoss", loss_weight=1.0, class_weight=[1.0] * num_classes
        ),
    ),
    train_cfg=None,
    test_cfg=dict(average_clips="prob"),
)

# 数据集配置
dataset_type = "PoseRgbDataset"
TrainVal_ann_file = "/media/pyl/WD_Blue_1T/All_proj/classify_cheat/Fusion_datasets/trainval_yolopose.pkl"
Test_ann_file = "/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/test_trainval_yolopose.pkl"

train_pipeline = [
    dict(type="fusionPreNormalize2D", left_idx=11, right_idx=12),
    dict(type="GenSkeFeat", dataset="coco", feats=["b"]),
    dict(type="UniformSampleFrames", clip_len=35, seed=255),
    dict(type="SimplifiedFormatGCNInput"),
    dict(
        type="SimplyResize",
        scale=(224, 224),
        use_padding=True,
        pad_val=(128, 128, 128),
        load_Resized_img=True,
    ),
    dict(type="ColorJitter"),
    dict(type="Flip", flip_ratio=0.5),
    dict(type="FormatShape", input_format="NCHW"),
    dict(type="PackActionInputs", collect_keys=("keypoint", "imgs")),
]

val_pipeline = [
    dict(type="fusionPreNormalize2D", left_idx=11, right_idx=12),
    dict(type="GenSkeFeat", dataset="coco", feats=["b"]),
    dict(type="UniformSampleFrames", clip_len=35, test_mode=True),
    dict(type="SimplifiedFormatGCNInput"),
    dict(
        type="SimplyResize",
        scale=(224, 224),
        use_padding=True,
        pad_val=(128, 128, 128),
        load_Resized_img=True,
    ),
    dict(type="FormatShape", input_format="NCHW"),
    dict(type="PackActionInputs", collect_keys=("keypoint", "imgs")),
]

test_pipeline = [
    dict(type="fusionPreNormalize2D", left_idx=11, right_idx=12),
    dict(type="GenSkeFeat", dataset="coco", feats=["b"]),
    dict(type="UniformSampleFrames", clip_len=35, test_mode=True),
    dict(type="SimplifiedFormatGCNInput"),
    dict(
        type="SimplyResize", scale=(224, 224), use_padding=True, pad_val=(128, 128, 128)
    ),
    dict(type="FormatShape", input_format="NCHW"),
    dict(
        type="PackActionInputs_Test",
        collect_keys=("keypoint", "imgs"),
        pred_txt=os.path.dirname(Test_ann_file),
        split_label=split_label,
    ),
]

# 训练参数配置
train_dataloader = dict(
    batch_size=4,  # 保持基础batch_size=4
    num_workers=0,
    sampler=dict(type="DefaultSampler", shuffle=True),
    dataset=dict(
        type=dataset_type,
        ann_file=TrainVal_ann_file,
        split="train",
        pipeline=train_pipeline,
    ),
)

val_dataloader = dict(
    batch_size=4,
    num_workers=0,
    sampler=dict(type="DefaultSampler", shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=TrainVal_ann_file,
        split="val",
        pipeline=val_pipeline,
        test_mode=True,
    ),
)

test_dataloader = dict(
    batch_size=64,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(type="DefaultSampler", shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=Test_ann_file,
        split=None,
        pipeline=test_pipeline,
        test_mode=True,
    ),
)

val_evaluator = [dict(type="AccMetric")]
test_evaluator = [dict(type="ClassifyReport")]

train_cfg = dict(
    type="EpochBasedTrainLoop", max_epochs=100, val_begin=1, val_interval=1
)
val_cfg = dict(type="ValLoop")
test_cfg = dict(type="TestLoop")

# AMP优化器配置（关键配置）
optim_wrapper = dict(
    type="AmpOptimWrapper",  # 启用自动混合精度训练
    loss_scale="dynamic",  # 动态损失缩放
    accumulative_counts=2,  # 梯度累积，等效batch_size=4*2=8
    optimizer=dict(
        type="AdamW", lr=0.001, betas=(0.9, 0.999), weight_decay=0.0001  # 基础学习率
    ),
    # 分层学习率策略
    paramwise_cfg=dict(
        custom_keys={
            "image_backbone": dict(lr_mult=0.1),
            "pose_backbone": dict(lr_mult=1.0),
            "fusion_neck": dict(lr_mult=1.0),
            "cls_head": dict(lr_mult=1.0),
        }
    ),
    clip_grad=dict(max_norm=10, norm_type=2),
)

# 学习率调度策略
param_scheduler = [
    dict(type="LinearLR", start_factor=0.01, by_epoch=True, begin=0, end=5),
    dict(
        type="CosineAnnealingLR",
        T_max=95,
        eta_min=1e-6,
        by_epoch=True,
        begin=5,
        end=100,
    ),
]

work_dir = "./work_dirs/pose_rgb_fusion_expE_amp_accum2"
find_unused_parameters = True

# ==============================================================================
# ExpE配置总结：
#
# 关键项：
# - AmpOptimWrapper（自动混合精度训练）
# - accumulative_counts=2（梯度累积，等效batch_size=8）
# - AdamW优化器（lr=0.001，适合AMP）
# - clip_grad=10（适中的梯度裁剪）
# - 动态损失缩放（loss_scale='dynamic'）
#
# 预期效果：
# - 减少显存使用（通过FP16）
# - 增大等效batch size（通过梯度累积）
# - 平滑梯度更新，提升训练稳定性
# - 保持训练精度，可能略微提升收敛速度
#
# 使用方法：
# python tools/train_m2.py --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_expE_amp_accum2.py --amp
# 注意：需要在命令行添加--amp参数来启用AMP训练
# ==============================================================================
