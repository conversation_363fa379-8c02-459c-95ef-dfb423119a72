# ==============================================================================
# ExpD: Focal重试（缓和欠拟合）配置文件
# 修正实验8欠拟合，兼顾稳定性与难例聚焦
#
# 核心改进：
# - SGD lr=0.01（适合Focal Loss的学习率）
# - gamma=1.5（降低难例聚焦强度，缓解欠拟合）
# - label_smoothing=0.05（防止过度自信）
# - 分层学习率：fusion_neck.lr_mult=0.8，cls_head.lr_mult=1.5
# - 温和预热：start_factor=0.1（适合SGD特性）
#
# 目标：修正实验8欠拟合，兼顾稳定性与难例聚焦
# 预期：异常检出率>97%，正常误判率<1.5%，训练更稳定
# ==============================================================================

import os

default_scope = "mmaction"

default_hooks = dict(
    runtime_info=dict(type="RuntimeInfoHook"),
    timer=dict(type="IterTimerHook"),
    logger=dict(type="LoggerHook", interval=20, ignore_last=False),
    param_scheduler=dict(type="ParamSchedulerHook"),
    checkpoint=dict(type="CheckpointHook", interval=1, save_best="auto"),
    sampler_seed=dict(type="DistSamplerSeedHook"),
    sync_buffers=dict(type="SyncBuffersHook"),
)

env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method="fork", opencv_num_threads=0),
    dist_cfg=dict(backend="nccl"),
)

log_processor = dict(type="LogProcessor", window_size=20, by_epoch=True)
vis_backends = [dict(type="LocalVisBackend")]
visualizer = dict(type="ActionVisualizer", vis_backends=vis_backends)

log_level = "INFO"
load_from = None
resume = False

# 基础配置
num_classes = 3
split_label = {0: [1, 2], 1: [0]}

# 模型配置
model = dict(
    type="MultiModalRecognizer",
    pose_backbone=dict(
        type="STGCN",
        in_channels=3,
        graph_cfg=dict(layout="coco", mode="stgcn_spatial"),
        init_cfg=dict(
            type="Pretrained",
            checkpoint="../stgcn_8xb16-bone-u100-80e_ntu120-xsub-keypoint-2d_20221129-131e63c3.pth",
        ),
    ),
    image_backbone=dict(
        type="MobileNetV2TSM",
        num_segments=3,
        is_shift=True,
        shift_div=8,
        pretrained2d=False,
        init_cfg=dict(
            type="Pretrained",
            checkpoint="../tsm_imagenet-pretrained-mobilenetv2_8xb16-1x1x8-100e_kinetics400-rgb_20230414-401127fd.pth",
        ),
        out_indices=(6,),
        frozen_stages=-1,
        norm_eval=False,
    ),
    fusion_neck=dict(
        type="MultiModalFusionNeck",
        pose_feat_dim=256,
        img_feat_dim=320,
        fusion_dim=512,
        fusion_type="attention",  # 保持attention融合
        dropout=0.5,
    ),
    cls_head=dict(
        type="TwoFusionHead",
        num_classes=num_classes,
        in_channels=512,
        loss_cls=dict(
            type="FocalLoss",  # 使用标准Focal Loss
            alpha=None,  # 不使用alpha权重，简化配置
            gamma=1.5,  # 降低难例聚焦强度，从2.0→1.5缓解欠拟合
            reduction="mean",
            label_smooth_eps=0.05,  # 标签平滑，防止过度自信
        ),
    ),
    train_cfg=None,
    test_cfg=dict(average_clips="prob"),
)

# 数据集配置
dataset_type = "PoseRgbDataset"
TrainVal_ann_file = "/media/pyl/WD_Blue_1T/All_proj/classify_cheat/Fusion_datasets/trainval_yolopose.pkl"
Test_ann_file = "/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/test_trainval_yolopose.pkl"

train_pipeline = [
    dict(type="fusionPreNormalize2D", left_idx=11, right_idx=12),
    dict(type="GenSkeFeat", dataset="coco", feats=["b"]),
    dict(type="UniformSampleFrames", clip_len=35, seed=255),
    dict(type="SimplifiedFormatGCNInput"),
    dict(
        type="SimplyResize",
        scale=(224, 224),
        use_padding=True,
        pad_val=(128, 128, 128),
        load_Resized_img=True,
    ),
    dict(type="ColorJitter"),
    dict(type="Flip", flip_ratio=0.5),
    dict(type="FormatShape", input_format="NCHW"),
    dict(type="PackActionInputs", collect_keys=("keypoint", "imgs")),
]

val_pipeline = [
    dict(type="fusionPreNormalize2D", left_idx=11, right_idx=12),
    dict(type="GenSkeFeat", dataset="coco", feats=["b"]),
    dict(type="UniformSampleFrames", clip_len=35, test_mode=True),
    dict(type="SimplifiedFormatGCNInput"),
    dict(
        type="SimplyResize",
        scale=(224, 224),
        use_padding=True,
        pad_val=(128, 128, 128),
        load_Resized_img=True,
    ),
    dict(type="FormatShape", input_format="NCHW"),
    dict(type="PackActionInputs", collect_keys=("keypoint", "imgs")),
]

test_pipeline = [
    dict(type="fusionPreNormalize2D", left_idx=11, right_idx=12),
    dict(type="GenSkeFeat", dataset="coco", feats=["b"]),
    dict(type="UniformSampleFrames", clip_len=35, test_mode=True),
    dict(type="SimplifiedFormatGCNInput"),
    dict(
        type="SimplyResize", scale=(224, 224), use_padding=True, pad_val=(128, 128, 128)
    ),
    dict(type="FormatShape", input_format="NCHW"),
    dict(
        type="PackActionInputs_Test",
        collect_keys=("keypoint", "imgs"),
        pred_txt=os.path.dirname(Test_ann_file),
        split_label=split_label,
    ),
]

# 训练参数配置
train_dataloader = dict(
    batch_size=4,
    num_workers=0,
    sampler=dict(type="DefaultSampler", shuffle=True),
    dataset=dict(
        type=dataset_type,
        ann_file=TrainVal_ann_file,
        split="train",
        pipeline=train_pipeline,
    ),
)

val_dataloader = dict(
    batch_size=4,
    num_workers=0,
    sampler=dict(type="DefaultSampler", shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=TrainVal_ann_file,
        split="val",
        pipeline=val_pipeline,
        test_mode=True,
    ),
)

test_dataloader = dict(
    batch_size=64,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(type="DefaultSampler", shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=Test_ann_file,
        split=None,
        pipeline=test_pipeline,
        test_mode=True,
    ),
)

val_evaluator = [dict(type="AccMetric")]
test_evaluator = [dict(type="ClassifyReport")]

train_cfg = dict(
    type="EpochBasedTrainLoop", max_epochs=100, val_begin=1, val_interval=1
)
val_cfg = dict(type="ValLoop")
test_cfg = dict(type="TestLoop")

# SGD优化器配置（适合Focal Loss）
optim_wrapper = dict(
    type="OptimWrapper",
    optimizer=dict(
        type="SGD", lr=0.01, momentum=0.9, weight_decay=0.0001  # SGD适合的学习率
    ),
    # 分层学习率策略
    paramwise_cfg=dict(
        custom_keys={
            "image_backbone": dict(lr_mult=0.1),  # 实际lr=0.001
            "pose_backbone": dict(lr_mult=1.0),  # 实际lr=0.01
            "fusion_neck": dict(lr_mult=0.8),  # 实际lr=0.008，提升融合模块学习
            "cls_head": dict(lr_mult=1.5),  # 实际lr=0.015，加速分类头学习
        }
    ),
    clip_grad=dict(max_norm=10, norm_type=2),
)

# 温和预热策略
param_scheduler = [
    dict(
        type="LinearLR",
        start_factor=0.1,  # 适合SGD的预热起始因子
        by_epoch=True,
        begin=0,
        end=5,
    ),
    dict(
        type="CosineAnnealingLR",
        T_max=95,
        by_epoch=True,
        begin=5,
        end=100,
        # 移除eta_min，让学习率自然衰减
    ),
]

work_dir = "./work_dirs/pose_rgb_fusion_expD_focal_g1p5_ls005"
find_unused_parameters = True

# ==============================================================================
# ExpD配置总结：
#
# 关键项：
# - SGD lr=0.01，momentum=0.9，weight_decay=0.0001
# - gamma=1.5（降低难例聚焦强度）
# - label_smoothing=0.05（防止过度自信）
# - fusion_neck.lr_mult=0.8（提升融合学习）
# - cls_head.lr_mult=1.5（加速分类头学习）
# - 预热start_factor=0.1（适合SGD特性）
#
# 预期效果：
# - 修正实验8的欠拟合问题
# - 保持Focal Loss的难例聚焦能力
# - 提升训练稳定性和收敛速度
# - 目标：异常检出率>97%，正常误判率<1.5%
# ==============================================================================
