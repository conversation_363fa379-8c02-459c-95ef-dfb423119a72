# ==============================================================================
# 实验11：训练稳定性优化配置文件
# 基于方案一：训练稳定性优化系列
#
# 优化策略：
# 1. 渐进式学习率预热：5epoch→10epoch，更温和的start_factor
# 2. 自适应梯度裁剪：max_norm从20降至10，提升训练稳定性
# 3. EMA权重平均：使用指数移动平均稳定模型权重更新
# 4. 增强正则化：优化dropout和权重衰减参数
#
# 创建时间：2025-02-06
# 基于文件：multimodal_poseGCN-rgbR50_fusion.py
# 实验目标：减少训练抖动，提升收敛稳定性，预期稳定性提升20-30%
# ==============================================================================

import os

default_scope = "mmaction"

# ==== 改进的Hook配置，新增EMA权重平均 ====
default_hooks = dict(
    runtime_info=dict(type="RuntimeInfoHook"),
    timer=dict(type="IterTimerHook"),
    logger=dict(type="LoggerHook", interval=20, ignore_last=False),
    param_scheduler=dict(type="ParamSchedulerHook"),
    checkpoint=dict(type="CheckpointHook", interval=1, save_best="auto"),
    sampler_seed=dict(type="DistSamplerSeedHook"),
    sync_buffers=dict(type="SyncBuffersHook"),
    # 新增：EMA权重平均，提升训练稳定性
    ema=dict(
        type="EMAHook",
        ema_type="ExponentialMovingAverage",
        momentum=0.999,
        update_buffers=True,
        priority="NORMAL",
    ),
)

env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method="fork", opencv_num_threads=0),
    dist_cfg=dict(backend="nccl"),
)

log_processor = dict(type="LogProcessor", window_size=20, by_epoch=True)

vis_backends = [dict(type="LocalVisBackend")]
visualizer = dict(type="ActionVisualizer", vis_backends=vis_backends)

log_level = "INFO"
load_from = None
resume = False

# ====================================================================================================================================

# 基础配置
num_classes = 3  # 类别数
split_label = {0: [1, 2], 1: [0]}  # 0:异常, 1:正常

# ==== 模型配置（微调正则化参数）====
model = dict(
    type="MultiModalRecognizer",
    pose_backbone=dict(
        type="STGCN",  # 或其他姿态骨干网络 PoseC3D
        in_channels=3,  # xyc * ['j', 'b']           num_class=256
        graph_cfg=dict(layout="coco", mode="stgcn_spatial"),  # COCO 17个关键点
        init_cfg=dict(
            type="Pretrained",
            checkpoint="../stgcn_8xb16-bone-u100-80e_ntu120-xsub-keypoint-2d_20221129-131e63c3.pth",
        ),
    ),
    image_backbone=dict(
        type="MobileNetV2TSM",
        # --- MobileNetV2TSM 专属参数 ---
        num_segments=3,
        is_shift=True,
        shift_div=8,
        pretrained2d=False,
        init_cfg=dict(
            type="Pretrained",
            checkpoint="../tsm_imagenet-pretrained-mobilenetv2_8xb16-1x1x8-100e_kinetics400-rgb_20230414-401127fd.pth",
        ),
        # 使用模型自带的2D预训练加载逻辑
        # 当 pretrained2d=True 时，init_weights会使用这个路径 mmcls://mobilenet_v2
        out_indices=(6,),
        frozen_stages=-1,
        norm_eval=False,
    ),
    fusion_neck=dict(
        type="MultiModalFusionNeck",  # 适配器
        pose_feat_dim=256,  # ST-GCN 输出维度(num_class=256)
        img_feat_dim=320,  # TSM为1280
        fusion_dim=512,
        fusion_type="attention",  # 跨模态融合cross_modal < attention
        dropout=0.55,  # 优化：从0.5提升到0.55，增强正则化效果
    ),
    cls_head=dict(
        type="TwoFusionHead",  # 双流融合特征分类头
        num_classes=num_classes,  # 类别
        in_channels=512,
        loss_cls=dict(
            type="CrossEntropyLoss",
            loss_weight=1.0,
            class_weight=[1.0] * num_classes,  # TODO 类别权重
            # class_weight=[1.0,1.5,1.0]  # TODO 类别权重
        ),
    ),
    train_cfg=None,
    test_cfg=dict(average_clips="prob"),
)

# 数据集配置（保持不变）
dataset_type = "PoseRgbDataset"
TrainVal_ann_file = "/media/pyl/WD_Blue_1T/All_proj/classify_cheat/Fusion_datasets/trainval_yolopose.pkl"
Test_ann_file = "/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/test_trainval_yolopose.pkl"

train_pipeline = [
    # 归一化：将坐标从像素空间 [0, W] 和 [0, H] 映射到了归一化空间 [-1, 1]； 中心化：将所有点的坐标变为相对于"髋部中心点"的坐标。
    dict(type="fusionPreNormalize2D", left_idx=11, right_idx=12),
    # feats=['b']: JointToBone
    dict(
        type="GenSkeFeat", dataset="coco", feats=["b"]
    ),  # 2S-GCN ['j','b', 'bm', 'jm']
    dict(type="UniformSampleFrames", clip_len=35, seed=255),  # 均匀采样
    dict(type="SimplifiedFormatGCNInput"),
    # 策略3： Padding
    dict(
        type="SimplyResize",
        scale=(224, 224),
        use_padding=True,
        pad_val=(128, 128, 128),
        load_Resized_img=True,
    ),
    dict(type="ColorJitter"),
    dict(type="Flip", flip_ratio=0.5),  # TODO 同时会翻转预处理后的骨骼
    dict(type="FormatShape", input_format="NCHW"),
    # 打包成模型输入的字典
    dict(type="PackActionInputs", collect_keys=("keypoint", "imgs")),
]

val_pipeline = [
    dict(type="fusionPreNormalize2D", left_idx=11, right_idx=12),  # 归一化、中心化
    dict(
        type="GenSkeFeat", dataset="coco", feats=["b"]
    ),  # 2S-GCN ['j','b', 'bm', 'jm']
    dict(type="UniformSampleFrames", clip_len=35, test_mode=True),  # 均匀采样
    dict(type="SimplifiedFormatGCNInput"),
    # 策略3 padding
    dict(
        type="SimplyResize",
        scale=(224, 224),
        use_padding=True,
        pad_val=(128, 128, 128),
        load_Resized_img=True,
    ),
    dict(type="FormatShape", input_format="NCHW"),
    dict(type="PackActionInputs", collect_keys=("keypoint", "imgs")),
]

test_pipeline = [
    dict(type="fusionPreNormalize2D", left_idx=11, right_idx=12),  # 归一化、中心化
    dict(
        type="GenSkeFeat", dataset="coco", feats=["b"]
    ),  # 2S-GCN ['j','b', 'bm', 'jm']
    dict(type="UniformSampleFrames", clip_len=35, test_mode=True),  # 均匀采样
    dict(type="SimplifiedFormatGCNInput"),
    # 策略3 padding
    dict(
        type="SimplyResize", scale=(224, 224), use_padding=True, pad_val=(128, 128, 128)
    ),
    dict(type="FormatShape", input_format="NCHW"),
    dict(
        type="PackActionInputs_Test",
        collect_keys=("keypoint", "imgs"),
        pred_txt=os.path.dirname(Test_ann_file),
        split_label=split_label,
    ),
]

# 训练参数配置
train_dataloader = dict(
    batch_size=4,
    num_workers=0,
    sampler=dict(type="DefaultSampler", shuffle=True),
    dataset=dict(
        type=dataset_type,
        ann_file=TrainVal_ann_file,
        split="train",
        pipeline=train_pipeline,
    ),
)

val_dataloader = dict(
    batch_size=4,
    num_workers=0,
    sampler=dict(type="DefaultSampler", shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=TrainVal_ann_file,
        split="val",
        pipeline=val_pipeline,
        test_mode=True,
    ),
)

test_dataloader = dict(
    batch_size=64,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(type="DefaultSampler", shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=Test_ann_file,  # test_trainval_yolopose.pkl
        split=None,  # 不进行数据划分
        pipeline=test_pipeline,
        test_mode=True,
    ),
)

val_evaluator = [dict(type="AccMetric")]
test_evaluator = [dict(type="ClassifyReport")]  # 自定义分类报告 ClassifyReport

train_cfg = dict(
    type="EpochBasedTrainLoop", max_epochs=100, val_begin=1, val_interval=1
)  # 按轮次(Epoch)训练
val_cfg = dict(type="ValLoop")
test_cfg = dict(type="TestLoop")  # TestLoop

# ==== 优化的优化器配置 ====
optim_wrapper = dict(
    type="OptimWrapper",  # 默认为 'OptimWrapper'
    # 优化器配置
    optimizer=dict(
        type="AdamW",
        lr=0.001,  # 基础学习率保持不变
        betas=(0.9, 0.999),
        weight_decay=0.00015,  # 优化：从0.0001增加到0.00015，增强正则化
    ),
    # 分层学习率配置（保持不变）
    paramwise_cfg=dict(
        custom_keys={  # key 是模型中模块的属性名 (e.g., self.image_backbone)
            "image_backbone": dict(lr_mult=0.1),  # 学习率 = 基础学习率 * 0.1
            "pose_backbone": dict(lr_mult=1.0),  # 其他模块使用基础学习率
            "fusion_neck": dict(lr_mult=1.0),  # 融合模块 默认学习率
            "cls_head": dict(lr_mult=1.0),  # 分类头 默认学习率
        }
    ),
    # 优化：自适应梯度裁剪，从max_norm=20降至10，提升训练稳定性
    clip_grad=dict(max_norm=10, norm_type=2),
)

# ==== 优化的学习率调度策略：渐进式预热 ====
# 总共训练 100 个 epochs，优化预热策略：5epoch→10epoch
param_scheduler = [
    # 第一阶段：极温和预热 (0-3 epochs)
    dict(
        type="LinearLR",
        start_factor=0.001,  # 优化：从0.01降至0.001，更温和的启动
        by_epoch=True,  # 按 Epoch 计数
        begin=0,
        end=3,  # 前3个epochs极温和预热
    ),
    # 第二阶段：渐进预热 (3-10 epochs)
    dict(
        type="LinearLR",
        start_factor=0.01,  # 第二阶段的起始因子
        by_epoch=True,
        begin=3,
        end=10,  # 优化：扩展到10个epochs完成完整预热
    ),
    # 余弦退火 (10-100 epochs)
    dict(
        type="CosineAnnealingLR",
        T_max=90,  # 优化：100 - 10 = 90 epochs
        eta_min=1e-6,
        by_epoch=True,  # 按 Epoch 计数
        begin=10,  # 优化：从第10个epoch开始
        end=100,
    ),
]

# ==== 实验11专用工作目录 ====
work_dir = "./work_dirs/pose_rgb_fusion_experiment11_stability"

find_unused_parameters = True

# ==============================================================================
# 实验11配置总结：
#
# 主要优化点：
# 1. 渐进式预热：分两阶段预热，0-3epoch (start_factor=0.001)，3-10epoch (start_factor=0.01)
# 2. 自适应梯度裁剪：max_norm从20降至10，减少梯度爆炸风险
# 3. EMA权重平均：添加EMAHook，momentum=0.999，稳定权重更新
# 4. 增强正则化：dropout 0.5→0.55，weight_decay 0.0001→0.00015
# 5. 专用工作目录：pose_rgb_fusion_experiment11_stability
#
# 预期效果：
# - 减少训练前期的剧烈抖动
# - 提升整体训练稳定性20-30%
# - 降低梯度范数的毛刺现象
# - 改善训练验证曲线的平滑度
#
# 使用方法：
# python tools/train_m2.py --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_experiment11_stability.py
# ==============================================================================
