Collections:
- Name: X3D
  README: configs/recognition/x3d/README.md
  Paper:
    URL: https://arxiv.org/abs/2004.04730
    Title: "X3D: Expanding Architectures for Efficient Video Recognition"

Models:
  - Name: x3d_s_13x6x1_facebook-kinetics400-rgb
    Config: configs/recognition/x3d/x3d_s_13x6x1_facebook-kinetics400-rgb.py
    In Collection: X3D
    Metadata:
      Architecture: X3D_S
      FLOPs: 2967543760
      Parameters: 3794322
      Resolution: 160x160
    Modality: RGB
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/pyslowfast/x3d_models/x3d_s.pyth
      Code: https://github.com/facebookresearch/SlowFast/
    Results:
    - Dataset: Kinetics-400
      Task: Action Recognition
      Metrics:
        Top 1 Accuracy: 73.3
    Weights: https://download.openmmlab.com/mmaction/v1.0/recognition/x3d/facebook/x3d_s_13x6x1_facebook-kinetics400-rgb_20201027-623825a0.pth
    reference top1 10-view: 73.1 [[SlowFast](https://github.com/facebookresearch/SlowFast/blob/master/MODEL_ZOO.md)]
    reference top1 30-view: 73.5 [[SlowFast](https://github.com/facebookresearch/SlowFast/blob/master/MODEL_ZOO.md)]

  - Name: x3d_m_16x5x1_facebook-kinetics400-rgb
    Config: configs/recognition/x3d/x3d_m_16x5x1_facebook-kinetics400-rgb.py
    In Collection: X3D
    Metadata:
      Architecture: X3D_M
      FLOPs: 6490866832
      Parameters: 3794322
      Resolution: 224x224
    Modality: RGB
    Converted From:
      Weights: https://dl.fbaipublicfiles.com/pyslowfast/x3d_models/x3d_m.pyth
      Code: https://github.com/facebookresearch/SlowFast/
    Results:
    - Dataset: Kinetics-400
      Task: Action Recognition
      Metrics:
        Top 1 Accuracy: 76.4
    Weights: https://download.openmmlab.com/mmaction/v1.0/recognition/x3d/facebook/x3d_m_16x5x1_facebook-kinetics400-rgb_20201027-3f42382a.pth
    reference top1 10-view: 75.1 [[SlowFast](https://github.com/facebookresearch/SlowFast/blob/master/MODEL_ZOO.md)]
    reference top1 30-view: 76.2 [[SlowFast](https://github.com/facebookresearch/SlowFast/blob/master/MODEL_ZOO.md)]
